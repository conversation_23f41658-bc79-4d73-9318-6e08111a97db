from openai import OpenAI
import async<PERSON> # Crucial for running blocking API calls in a thread
from typing import Dict, Any, Optional

class DeepSeek_LLM:
    def __init__(self, api_key: str, model: str = "deepseek/deepseek-r1-0528:free", temperature: float = 0.7,
                 base_url: str = "https://openrouter.ai/api/v1",
                 site_url: Optional[str] = None, site_name: Optional[str] = None):
        """
        Initializes the DeepSeek_LLM client for OpenRouter.

        Args:
            api_key (str): Your OpenRouter API key.
            model (str): The DeepSeek model to use (default: "deepseek/deepseek-r1-0528:free").
                         Note: For multimodal capabilities, you would need to use a DeepSeek-VL
                         or Janus-Pro model if supported by OpenRouter's API for image input.
            temperature (float): The sampling temperature (default: 0.7).
            base_url (str): The base URL for the OpenRouter API (default: "https://openrouter.ai/api/v1").
            site_url (Optional[str]): Optional. Your site URL for rankings on openrouter.ai.
            site_name (Optional[str]): Optional. Your site title for rankings on openrouter.ai.
        """
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.base_url = base_url
        self.site_url = site_url
        self.site_name = site_name

        # Initialize the OpenAI client pointing to OpenRouter
        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key,
        )

        # Prepare extra headers for OpenRouter
        self.extra_headers = {}
        if self.site_url:
            self.extra_headers["HTTP-Referer"] = self.site_url
        if self.site_name:
            self.extra_headers["X-Title"] = self.site_name

    async def call(self, query: str, **kwargs: Any) -> Dict[str, Any]:
        """
        Makes an asynchronous call to the OpenRouter API using a DeepSeek model for chat completions.

        Args:
            query (str): The user's prompt.
            **kwargs (Any): Additional keyword arguments to pass to the
                            client.chat.completions.create method. This can include
                            'extra_headers' or other parameters like 'max_tokens'.

        Returns:
            Dict[str, Any]: A dictionary containing the model's response,
                            typically {'text': <generated_text>}.
                            Returns {'error': <error_message>} on failure.
        """
        messages = [
            {"role": "user", "content": query}
        ]

        # Combine configured extra_headers with any headers passed in kwargs
        # Pop 'extra_headers' from kwargs to prevent it from being passed twice
        combined_extra_headers = {**self.extra_headers, **kwargs.pop('extra_headers', {})}

        try:
            # The actual API call is synchronous, so run it in a separate thread
            completion = await asyncio.to_thread(
                self.client.chat.completions.create,
                model=self.model,
                messages=messages,
                temperature=self.temperature,
                extra_headers=combined_extra_headers,
                extra_body={}, # As per your original example
                **kwargs # Pass any other additional keyword arguments
            )
            return {'text': completion.choices[0].message.content}
        except Exception as e:
            print(f"Error calling DeepSeek API via OpenRouter: {e}")
            return {'error': str(e)}