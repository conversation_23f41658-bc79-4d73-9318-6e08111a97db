
const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();

    // SLIDE 1: Capybara Overview (Title Slide)
    const slide1 = pptx.addSlide();
    slide1.background = { color: 'FFFFFF' }; // Extracted from .slide-container background-color

    // TITLE
    slide1.addText('Capybara Overview', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 36, // Shorter title, larger font
        color: '2C3E50', // Extracted from .main-title color
        bold: true
    });

    // SUBTITLE
    slide1.addText('A Comprehensive Look at the World\'s Largest Rodent', {'
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 18, // h2=18
        color: '34495E' // Extracted from .subtitle color
    });

    // PRESENTER INFO (Left, lower part of slide)
    slide1.addText('Presenter: Tech Consultant', {
        x: 0.5, y: 3.2, w: 4.0, h: 0.6,
        fontSize: 14, // p=14
        color: '555555' // Extracted from .presenter-info p color
    });

    // DATE INFO (Left, lower part of slide, below presenter)
    slide1.addText('Date: October 26, 2023', {
        x: 0.5, y: 3.9, w: 4.0, h: 0.6,
        fontSize: 14, // p=14
        color: '555555' // Extracted from .presenter-info p color
    });

    // COMPANY LOGO PLACEHOLDER (Right, lower part of slide)
    slide1.addText('[Tech Consulting Logo]', {
        x: 5.25, y: 3.9, w: 4.0, h: 0.6,
        fontSize: 14, // Based on 0.9em, similar to p
        color: '777777' // Extracted from .company-logo-placeholder color
    });

    // SLIDE 2: Capybaras: Habitat & Diet
    const slide2 = pptx.addSlide();
    slide2.background = { color: 'FFFFFF' }; // Extracted from .slide-container background-color

    // TITLE
    slide2.addText('Capybaras: Habitat & Diet', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, // Medium length title
        color: '2C3E50', // Extracted from .slide-title color
        bold: true
    });

    // LEFT COLUMN CONTENT
    // Heading 1: The Largest Rodent
    slide2.addText('The Largest Rodent', {
        x: 0.5, y: 1.8, w: 4.0, h: 0.6,
        fontSize: 16, // h3=16
        color: '34495E', // Extracted from .key-point-heading color
        bold: true
    });

    // Description 1
    slide2.addText('Capybaras (Hydrochoerus hydrochaeris) are the world\'s largest rodents. Semi-aquatic, native to South America.', {'
        x: 0.5, y: 2.5, w: 4.0, h: 0.6,
        fontSize: 14, // p=14
        color: '555555' // Extracted from .key-point-description color
    });

    // Heading 2: Habitat - Water-Centric Environments
    slide2.addText('Habitat - Water-Centric Environments', {
        x: 0.5, y: 3.2, w: 4.0, h: 0.6,
        fontSize: 16, // h3=16
        color: '34495E', // Extracted from .key-point-heading color
        bold: true
    });

    // Description 2 (with bullets)
    slide2.addText([
        { text: 'Found in South America, always near water sources:', options: { fontSize: 14, color: '555555' } },
        { text: '* Rivers, Lakes, Swamps, Marshes, Ponds.', options: { fontSize: 14, color: '555555' } },
        { text: 'Water provides refuge and food.', options: { fontSize: 14, color: '555555' } }
    ], {
        x: 0.5, y: 3.9, w: 4.0, h: 0.6 // This block uses the last available Y position
    });

    // RIGHT COLUMN CONTENT (Image)
    slide2.addImage({
        path: 'https://source.unsplash.com/600x700/?capybara,water,grass,wildlife',
        x: 5.25, y: 1.8, w: 4.0, h: 2.7 // Aligned with left column content, max height to fit
    });

    return pptx.writeFile({ fileName: 'presentation_20250713_223357.pptx' });
}

// Execute the presentation creation
createPresentation()
    .then(() => {
        console.log('PowerPoint file generated successfully!');
        console.log('File saved as: presentation_20250713_223357.pptx');
        process.exit(0);
    })
    .catch(error => {
        console.error('Error generating presentation:', error);
        process.exit(1);
    });
