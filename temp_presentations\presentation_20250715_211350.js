
const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    // Ensure PptxGenJS is available in the environment where this code runs.
    // For example, in a browser: <script src="https://unpkg.com/pptxgenjs@latest/dist/pptxgen.min.js"></script>
    // Or in Node.js: const PptxGenJS = require('pptxgenjs');
    
    function createPresentation() {
        const pptx = new PptxGenJS();
    
        // SLIDE 1: Title Slide
        const slide1 = pptx.addSlide();
        slide1.background = { color: 'F5F5F5' }; // Extracted from body background gradient (#F5F5F5)
    
        // TITLE
        slide1.addText('All About Guinea Pigs', {
            x: 0.5, y: 0.8, w: 9.0, h: 0.8,
            fontSize: 32, // Title fontSize: 28-36 based on text length (shorter = larger)
            color: '222222', // Extracted from .slide-title
            bold: true
        });
    
        // CONTENT 1: Subtitle
        slide1.addText('Understanding Your Charming Companion', {
            x: 0.5, y: 1.8, w: 9.0, h: 0.6,
            fontSize: 18, // Content fontSize: h2=18
            color: '555555' // Extracted from .slide-subtitle
        });
    
        // CONTENT 2: Presenter Info
        slide1.addText('Presenter: Tech Consultant Team', {
            x: 0.5, y: 2.5, w: 9.0, h: 0.6,
            fontSize: 14, // Content fontSize: p=14
            color: '666666' // Extracted from .info-block
        });
    
        // CONTENT 3: Date Info
        slide1.addText('Date: October 26, 2023', {
            x: 0.5, y: 3.2, w: 9.0, h: 0.6,
            fontSize: 14, // Content fontSize: p=14
            color: '666666' // Extracted from .info-block
        });
    
        // SLIDE 2: Executive Summary
        const slide2 = pptx.addSlide();
        slide2.background = { color: 'F5F5F5' }; // Extracted from body background gradient (#F5F5F5)
    
        // TITLE
        slide2.addText('Guinea Pigs: Charming Companions', {
            x: 0.5, y: 0.8, w: 9.0, h: 0.8,
            fontSize: 30, // Title fontSize: 28-36 based on text length
            color: '222222', // Extracted from .slide-title
            bold: true
        });
    
        // CONTENT 1: Origin & Nature (Left Column, Row 1)
        slide2.addText('Origin & Nature: Social, vocal rodents native to the Andes Mountains.', {
            x: 0.5, y: 1.8, w: 4.0, h: 0.6,
            fontSize: 16, // Combined h3 and p, so 16
            color: '444444', // Extracted from .slide-subtitle for headings
            bold: true // Bold for headings
        });
    
        // CONTENT 2: Key Needs (Right Column, Row 1)
        slide2.addText('Key Needs: Spacious housing, unlimited hay, fresh veggies, daily Vitamin C.', {
            x: 5.25, y: 1.8, w: 4.0, h: 0.6,
            fontSize: 16, // Combined h3 and p, so 16
            color: '444444', // Extracted from .slide-subtitle for headings
            bold: true // Bold for headings
        });
    
        // CONTENT 3: Behavior (Left Column, Row 2)
        slide2.addText('Behavior: Highly social, vocal communication (\'wheeking\'), and \'popcorning\' when happy.', {
            x: 0.5, y: 2.5, w: 4.0, h: 0.6,
            fontSize: 16, // Combined h3 and p, so 16
            color: '444444', // Extracted from .slide-subtitle for headings
            bold: true // Bold for headings
        });
    
        // CONTENT 4: Care Tip (Right Column, Row 2)
        slide2.addText('Care Tip: Daily interaction, gentle handling, and environmental enrichment are crucial.', {
            x: 5.25, y: 2.5, w: 4.0, h: 0.6,
            fontSize: 16, // Combined h3 and p, so 16
            color: '444444', // Extracted from .slide-subtitle for headings
            bold: true // Bold for headings
        });
    
        // CONTENT 5: Additional Point (Left Column, Row 3)
        slide2.addText('* Solitary guinea pigs can become lonely and depressed.', {
            x: 0.5, y: 3.2, w: 4.0, h: 0.6,
            fontSize: 14, // p=14
            color: '555555' // Extracted from .slide-text
        });
    
        return pptx.writeFile({ fileName: 'presentation_20250715_211350.pptx' });
    }
}

// Execute the presentation creation
createPresentation()
    .then(() => {
        console.log('PowerPoint file generated successfully!');
        console.log('File saved as: presentation_20250715_211350.pptx');
        process.exit(0);
    })
    .catch(error => {
        console.error('Error generating presentation:', error);
        process.exit(1);
    });
