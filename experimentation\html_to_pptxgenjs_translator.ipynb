{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# HTML to PptxGenJS Translator\n", "\n", "This notebook demonstrates how to translate HTML slides to PptxGenJS code."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import re\n", "import json\n", "from bs4 import BeautifulSoup\n", "import textwrap\n", "from IPython.display import display, HTML"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Add parent directory to path to import from your existing modules\n", "sys.path.append(\"../\")\n", "from pptx_generation.planner import Planner\n", "from pptx_generation.generation import Generator\n", "from htmlrender.renderer import HTMLRenderer\n", "from llm.llmwrapper import LLM"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:LLM initialized\n", "INFO:root:Provider: gemini\n", "INFO:root:Model: gemini-2.0-flash\n", "INFO:root:LLM initialized\n", "INFO:root:Provider: gemini\n", "INFO:root:Model: gemini-2.5-flash\n", "INFO:root:LLM initialized\n", "INFO:root:Provider: gemini\n", "INFO:root:Model: gemini-2.5-pro\n"]}], "source": ["# Initialize your existing components\n", "llm1 = LLM(provider=\"gemini\", model=\"gemini-2.0-flash\")\n", "llm2 = LLM(provider=\"gemini\", model=\"gemini-2.5-flash\")\n", "llm3 = LLM(provider=\"gemini\", model=\"gemini-2.5-pro\")\n", "\n", "planner = Planner()\n", "generator = Generator()\n", "renderer = HTMLRenderer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Generate HTML Slides\n", "\n", "First, we'll use your existing pipeline to generate HTML slides."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-07-13 03:45:43.518\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.planner\u001b[0m:\u001b[36mbrainstorm\u001b[0m:\u001b[36m25\u001b[0m - \u001b[1mPPTX Planning STEP 1: Brainstorming implementation approach based on user query...\n", "                    QUERY: \"Make a short presentation about AI-powered document search systems\"\n", "\u001b[0m\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Generating presentation plan...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "\u001b[32m2025-07-13 03:45:57.469\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.planner\u001b[0m:\u001b[36mbrainstorm\u001b[0m:\u001b[36m35\u001b[0m - \u001b[1mPPTX Planning STEP 1: Brainstorming complete!\u001b[0m\n", "\u001b[32m2025-07-13 03:45:57.469\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.planner\u001b[0m:\u001b[36moutline\u001b[0m:\u001b[36m70\u001b[0m - \u001b[1mPPTX Planning STEP 2: Creating high-level Presentation outline...\u001b[0m\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "\u001b[32m2025-07-13 03:46:11.745\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.planner\u001b[0m:\u001b[36moutline\u001b[0m:\u001b[36m74\u001b[0m - \u001b[1mPPTX Planning STEP 2: Outline creation complete!\u001b[0m\n", "\u001b[32m2025-07-13 03:46:11.745\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.planner\u001b[0m:\u001b[36mslide_content\u001b[0m:\u001b[36m111\u001b[0m - \u001b[1mPPTX Planning STEP 3: Defining the content to appear on each slide...\u001b[0m\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "\u001b[32m2025-07-13 03:46:32.962\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.planner\u001b[0m:\u001b[36mslide_content\u001b[0m:\u001b[36m115\u001b[0m - \u001b[1mPPTX Planning STEP 3: Slide content creation complete!\u001b[0m\n", "\u001b[32m2025-07-13 03:46:32.962\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.planner\u001b[0m:\u001b[36mextract_slide_content\u001b[0m:\u001b[36m143\u001b[0m - \u001b[1mPPTX Planning STEP 4: Extracting slide content...\u001b[0m\n", "\u001b[32m2025-07-13 03:46:32.976\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.planner\u001b[0m:\u001b[36mextract_slide_content\u001b[0m:\u001b[36m159\u001b[0m - \u001b[1mPPTX Planning STEP 4: Slide content extracted!\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Plan generated!\n"]}], "source": ["# Sample query to generate a presentation\n", "query = \"Make a short presentation about AI-powered document search systems\"\n", "\n", "# Generate presentation plan using the Planner\n", "print(\"Generating presentation plan...\")\n", "presentation_plan = planner.plan_content(query=query, llm_1=llm1)\n", "print(\"Plan generated!\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-07-13 03:49:35.596\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.generation\u001b[0m:\u001b[36mgenerate_title_slide\u001b[0m:\u001b[36m82\u001b[0m - \u001b[1mGenerating title slide...\u001b[0m\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "\u001b[32m2025-07-13 03:50:00.397\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.generation\u001b[0m:\u001b[36mgenerate_title_slide\u001b[0m:\u001b[36m90\u001b[0m - \u001b[1mReviewing generated HTML...\u001b[0m\n", "\u001b[32m2025-07-13 03:50:02.832\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1mSaving rendered HTML image to temp file: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n", "\u001b[32m2025-07-13 03:50:03.011\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mTemp file removed: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "\u001b[32m2025-07-13 03:50:46.980\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1mSaving rendered HTML image to temp file: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n", "\u001b[32m2025-07-13 03:50:47.009\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mTemp file removed: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n"]}, {"data": {"image/jpeg": "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", "image/png": "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", "text/plain": ["<PIL.Image.Image image mode=RGB size=640x360>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Generate title slide\n", "title_slide_content = presentation_plan['processed_slide_content']['slide_content']['slide_1']\n", "title_slide_html = generator.generate_title_slide(query=query, slide_content=title_slide_content, generator_llm=llm2, reviewer_llm=llm2)\n", "\n", "# Display the title slide\n", "title_slide_img = renderer.renderHTML(html_str=title_slide_html, resize=True, resize_ratio=0.5)\n", "display(title_slide_img)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-07-13 03:50:53.243\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.generation\u001b[0m:\u001b[36mgenerate_agenda_slide\u001b[0m:\u001b[36m136\u001b[0m - \u001b[1mGenerating Agenda slide...\u001b[0m\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "\u001b[32m2025-07-13 03:51:26.032\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.generation\u001b[0m:\u001b[36mgenerate_agenda_slide\u001b[0m:\u001b[36m145\u001b[0m - \u001b[1mReviewing generated HTML...\u001b[0m\n", "\u001b[32m2025-07-13 03:51:29.360\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1mSaving rendered HTML image to temp file: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n", "\u001b[32m2025-07-13 03:51:29.383\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mTemp file removed: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "\u001b[32m2025-07-13 03:52:14.448\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1mSaving rendered HTML image to temp file: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n", "\u001b[32m2025-07-13 03:52:14.460\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mTemp file removed: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n"]}, {"data": {"image/jpeg": "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", "image/png": "iVBORw0KGgoAAAANSUhEUgAAAoAAAAFoCAIAAABIUN0GAAEAAElEQVR4AWJ89Pk7wygYDYHREBgNgdEQGA2B0RCgAWBkYOD5/48Rm8ksDAyMDAz/wSQ2+VGx0RAYDYHREBgNgdEQGA0BMgG+6pWFgeE/IwPjf1AdTKbxo9pGQ2A0BEZDYDQERkNgNATQALh3CybQJGBcJhhjlB4NgdEQGA2B0RAYDYHREKAa+E/IJFAFDOkhw0eoGZHGo0fZkAAcDYfRcBhNA6NpYDQNjKYBktIApFaFkJCgQyMhi7Dw9ZHRNIxyR0NgNARGQ2A0BEZDYDQEiAOMjAz/cS7CglTOkJ7yKHs0HEbTwGgaGE0Do2lgNA1QKw0wII0oY1bYLMjjz9SyctSc0eQ7mgZG08BoGhhNAyM8DRAcW2Z8DNoHDEknDOD9SPAKe4QH3Wg4jKaH0TQwmgZG08BoGqA0DTAyMHD//wuvZUHmwTC8AoZUtzDhUXo0BEZDYDQERkNgNARGQ4Ay8J+BkYnhPze+gzhA/WSs1TNlNo/qHg2B0RAYDYHREBgNgREcAox4q1cm0LAzovf7H8SFBtYoGxIQo+EwGg6jaWA0DYymgdE0QFYaQFSvkABEIRkff/6BIjDKGQ2B0RAYDYHREBgNgdEQoBLAMwcMOogDtFSLSjaNGjMaAqMhMBoCoyEwGgKjIUAMAFXAjHj7yMSYMqpmNARGQ2A0BEZDYDQERkMAGRBcWgWqgEfrX+QgG2WPhsBoCIyGwGgIjIYA5YBg3coCXqOF96wOyl0xasJoCIyGwGgIjIbAaAiMvBD4/x+yEBq7z1n+Q+poCIldzajoaAiMhsBoCIyGwGgIjIYAOQBayWLTygIdpIZQkGp4lD0aDqNpYDQNjKaB0TQwmgaokQbwrLICbUMCbRTGVjmPio2GwGgIjIbAaAiMhsBoCJAHIHUrnm1I4B4wI2wjEjVqe5BDR80ZbTmOpoHRNDCaBkbTwIhPA4yMDHh6wKBV0NBeNqjmHMWjITAaAqMhMBoCoyEwGgLUAZAWCC6zGJ98/gHq/+JXhUv3qPhoCIyGwGgIjIbAaAiMhgAuAO4Bc+G4DQncA4bctgQZLRllj4bDaBoYTQOjaWA0DYymAaqkAVwVM1ic8cmX0bOgwSExSoyGwGgIjIbAaAiMhgC1ASMDA9c/7PcBs4AmgCFLtaht66h5oyEwGgKjITAaAqMhMKJDAG/1ysIAXgL9H97XHtFBNer50RAYDYHREBgNgdEQoA6AVr5QCouZoDng0QVYWAJmVGg0BEZDYDQERkNgNAQoAATrVvA+YAosGNU6GgKjITAaAqMhMBoCoyGAC+AZX2YhWEXjMpRCcUYGhn8g8B80RI7NLCYmZiZG0Aw1Nkn6iYEGD/6DzvJkAl1bwfD///+///4xMDAwMTExMeLZYE2BC/////fvHyxeGEHWMILsholQYPKo1tEQGA2B0RAYDYFBAwasB/zvPwMnNycnE8M/HFcxffvx69ef/6CaZyAC6z/IVaDK9+/ff6zs7MwM/379/svA8J+JlZ2fnZGJgeH7rz/ff/1hAjGpUDOCjWBkBLVJGFjY2HnYGVnAvv7HwPDj55+fv34zMDGBXANyFVhilBgNgdEQGA2B0RAYAoART0cS1AOGlOz09Mj//4ycrP9P7d1++uYrTg42RnAPD+YAUN/4LyObrbuHijDrr7/gugkmR0/6P8N/BkYmAT72Z4/uv/7JpSYr9IeB8cvTG6v2n/n8j93U1t5ESfj7r7/UaiIwMvz/x8DExc32+vHd3Wcuv/zwg5GRgVtA1NDYUFVG6OePH/9Ah5oN/JAAPaNg1K7REBgNgdEQGLoAXLfiq8IgPWB8VTQtPP/3PwMny/9Tezb3rrskzMP6/uOXf4ihaCbG/79/sPBN0XfSFmf/8fsPqOuHVEODx4NBW8SRaz64IGRRN5qbQQEAGkVmYEAyh4GBAVTFguQQoiAeSCV4aTgDI+Ofb3vXbuqas80js9pAWfjzP5bPD853trQ+/ylYNUXTTkP8y4/fLEyM6MaCTECxC2YRiiCaI//9Z+Ri+7t14ZRpS/c+f/X2/efv/xmZePkFhIVFfeOSsiOdGH/+gA4IoJoPczPUcBgX5CkYGyoF9jID5IJK5GkJaOiBdIDG2EEOA7MR6tHDG6QEYTiGLNxAkF1gWTgDl70gE0fxaAiMhsBoCAwvwIh32BIy0gkqS+npa1C74D8jFy+fsACvmKxqoqcNL/O/v9A6GNoD1hFn+/WXgY0V5ML///7+BXcA//9nYGZmhvSY//4F947////3/z8zCwtojvb/v99//v5nZASxYf759+8fIyMTCwvUnD9//jGCJpdB0sxMIKMYYNO68JldBob/f37/ZuXmenN2d2PXrCd/RYM52ZmZWP7//MOnaFRVX/flL7uxltj3P//YWVkZGf//+wufsgUby8wCatFAZ3L///v3n4mZhZmZkeH//z9//vxjAE3rgtQh4X9//3Hycl7ZsaxtypqfrPzatq4OJhos/76dPXTozM0Xy6ZNEZWQTnBR/fzlJwsLCxMzKPzgljIxMjIxMzMwgO37zwDn/vn9hwHicbC9/xmZ/v//x8TEzMzC/P/f3z9//zExgdbAMzAwMrOATPz398+ff/9B5jMy/v37B9Ik+s/AwMLCwggKENB8AKxeBo2VMzIxs7Awgzzx//8fkHqYvxgZWcDu+fPnL2gin4kBHAIIF4K0gDEkKv+D57zBAqPEaAiMhsBoCIwgAKqWBsi7///////71w8GbvHIhDBpVobf/8FjrKCOKQMTA8PPn7++fv36488/ZkYGFnZOTlam33//s7Iwfvv67e///3///ufm5gLNIDOx8XIxf/7w6evvf0wsrAIC3P9+/f7x6y94zhRU9HNyczL8/vnxM6iTzcrBKcDH+f3rj7//GZkYGX58//b7zz8mZhYODjZQO4WR4ffPHz9//WFkYuZgZ/33+/enz9/ZuXi4frH8+P7ty/fvv34xcksoh0epMTKAlmJ9+fzl5++/jExMHByczIyQbikDI8P/r1+//PvHwMzKysnG8peBiYeX7fvnL5+/gozlE+Bl+f/36/ffEOfBQ/4/Ewvrr6/7Dhz98ptZxdxhUn+hJBvDbwaGSE+zzPSyA1cfL125xc+umJuZ4ecv0Nw4AxMTOwcHqNpkYPj75/fXn7/+MzCys3OwsjD++f0LNGfMyMwvyMf899f7j5//M7MK8fP8+vmLlYPz7/dvHz/9ZOXg4ufh/P7tByjE///99uXb33//Obh5+XlZPr7/+P33Px4+fk6mf7/+MXOy/nv//tNfBhZ+QV6G39BZ+X//Gbh5OP98//7pyzdQ8w7UUwf56/vP34xMjP///P7y89c/BiY+Ab6/3798+vmfm4Ply7ef//4xcXCws7BAQ4qRkeHHt2+///5jZmbh4GQbHVuHJ4ZRxmgIjIbACAEDWAHDQhg6rgoqySFCjAwMv3/9+f2f5d+HB9XlHbde/5TQsOzqKFDkZbt5bFNZ6+K33/4YeEW3FwUz/f3H+Ov9ylmrNu059ezdN05+IWML6+joQFVRjm8//zAwMHJzsV4+unv52p2Xbz359odRVFLKxtkjOsSZk/EvJ9PPef31i/fekTbzmNmSzfX/JzcPy5r5U/uXHuGVMJiyrOXjtrlF3Wv+snPzsf5ZO7Fp5RTu+mnzDD7tjiud+vw3T35zd4jko4zc7me/uFPKqlM9ND9+/MbBx/3o2ObCpjlvGQQLGprDLGR//fi6e/XKlZsP3n/2iZWTR9vYJDwixFRV9Nu3n4ygDih07IGRifHf379fvn5lZmP9+v7V3QdvBJRFGBkYeCQ1M/My9K4+55NQ+vP9F5cgx8H5U9tn7WCTVGnubzEWY/nLznF378bSjkUfWcUK6ppiLWX2bVtW3bucV8+9Isl6x/KVx689Y+TgNnf1K07zv3Vwy9S56+68+sYnLOkfHRXpZvzvL8O/r3ca0xsOv2fJKcgSfX127sYjX34ySKloZ+ZnGwl+7J00e8/pO/9YOPUsHbPSwmR4mX/8YeDjYj65a8PS9ftuP3z58y8jByeXso5+dGyUsSL/X2a2V5f3ZlRP/SeolRJrv3fV2tvv/qmoyb99+PDpq08usVm16a4/P39h5uD58fhUflHv9fcMsfnleUGGn7/8RGuUQBLDKDkaAqMhMBoCwxUMUAUM6m+CphsZmVj/fn62bMEq+BA0IxPTn58/xFT1na10BJX0E4LtSnrW3jy9b/Yai+44nVkzl997+V5QTi852ouT4d+/v+8mNjUt2HGJlYdXRIDnw/OHaxZcO3Xpdl9XpSo/EyM727ltS4pbF775ziAgJMTJ/PfetStXLly+8/JLW0Eg09/vP79/+/TpM/+3H+DKHzQC/vvnj0+fPjPwfvv3/z/oEgvE5C1sPvTvn+/fv3//w/bt2w9JFQNteYGrR+7vP3Ai3EWLkYmJneH3kcNH7z15J2lobKIlyfz32+pp3d2L9v9j4xYW4vv+8dXONSuPnrzW2VNvpy749fsfRJXz9y8zD7eGqtK/w89+Pr9enFNoaWOhr6mqp69r7Ort5srwlYHhwyfQOqw/v398/vSZjfcbZPydEdwD/vz58yc27l9//jEyMvz+9ePr999/Hp6vKNnz5S87HwfT15fP1y1e8PzWyXvnz35m4udg/PX07tXe5h5u4Z4wc9lPn/5+/vzt/y+mlVN737z+xCvE//vH90vH9je/ey/F8OLkzXdiQtzfPr3Zvmz++78cEyvCuTn+Hd8wP6d27jdWXmEhfg7mv29evXiy+d7VO29mTa1REmb4+/sXaOTi76PpE2e9fPXp05dfWrZuxuwfr9y6f+LIyRfRziKMTOxs/0+fPHP9wTMmCX1zE41/oNHy4ZrFRv01GgKjITDSQwB57QtyWEBmAZFF6ML+Dx5o/v+fkYX19/v7E9q765t7m1t6m1p6m9v6a2ua52w8zsTO/OXzD7uQ2AhHDYZ/f05sXlHW2H/2/ntmDoG47HRDaa7/LExnt69fvfcyn6RSTl3zxg0L5/cVm6pJPL5wbN6a/Yxc7L9f3pw+Z+273+wGdp4zFs7YsHJKrKsW2/9fx3ZsOXLrAxcbK2SKlIWZCewaUBCB5zVZ2FhZf37+rucbO6M1mevflw8/mYPz6/esm24uy/Ltz382NhbQROn/3wxM3C4OZoKcTHcvnLzw8BMPF/uXNw9Pnr3zj53H0t5aRYDl9qk989ceZuITj8gqXLd+4cpZza4mSt+eXp2xcMtXBhZmpF3ETIz/P/9k9AqPcjcUe/fh66/P7/ds3NDfOykvuzA2tXrqljPvf4DO8v7PAJrgBlkPnrVlAN/0zMjICBJhZobMfDMyMrFxsP34+F5cz3XlhiUrZtToSnPxcTKdOnJWyzd157bFE6oihLk5WX59PnP60h8mBsb//5lYWNgZfn/6xV3cPWH3prnJ3gacnFwf7l25/VNixrJFW5Z0OWiLcfBw3D17/vHnX6z/f/9g4jIy0bV09pq9ZM6OrYvrk135udk/Pb994e47DtCKe0ZWVjaGvz+//uMJjourKM/NSg5ytzYQ4GR79+TmuVtvODg5/n//cvL0xS9/2XQsLHVkuGAbuuiS9kYtGQ2B0RAYDQH6AuhQJ4aloB4weMUQhgyNBUB9YEbQfCGroFpBbDwvbBEWuAf8XUzVgOHXHyaGf9/+8STnpJ2+VnP79eO9Bx7/+cVo6h0U6az++csPPva/x0+f//KXSVvPLNjZ8M/3LxpmDh42R68+OHT9wqWPf7w+3Lhw9/UXFm6RwNg4EzmhT38ZYlJTxfTvaeobSEtwfv/9G+xx6Ipd0FpdkJdBM9Og5sH//ywsrFxc7OBFwwxsHBx8PNwffoN67WAVoHHzHwwM+lbWast3nX7yZP/hi84qtlcvnLn2+B2PkIq9rQk3w78TJ86/+fpPWFMjNMCR488PXnXDYDfzM9ceP79x5e6r77rCzN9/g8wBWQsKip/Mgkq17a166zbtPHDm6ZuPnz5/+/jh07dL5zrOnb/5rKAq3hXcUIC5AexiyFJwiJNA1TNYkOHv318cAkHR4ZpiPP+EtY21pE5tuyUsrZWcECDMzcptYaUiuOHxu2+/fn4DDdMz/WdkZPz+9buJn0uwsyYrA4O9lf6yrSfe/OaOCgm31xBlYBC1NlLddfIBw59fP379/fmX2cYv0iUg8tff/39+/Xr3+sXr918ZGRn/gTrfv0DLwxj+MzAxfP/80zw4uq40lJOB4esfhh/GZprSm048fHPq1Hk/Y/eP9y6fvvKEiVvAxs6cl+nfh/+MyM0RUICM4tEQGA2B0RAY4gBUHoIwzh4wqAIGjbbSfQ0MqEUAWkD8m4VPKjY5TJqJ4RcDaO0V+AQMhj//Gb58+8nAzPLnxzceeb2StMC8jtVMrKzMgqrZ6SGcf35+YWb9+ePjy+fvOHj5Xp7d6eawGbJA9+/ffz9+/3h85cG7vwyvn739/u0vlwS/pCjn1x+/fv7+yymlGRuu9ePX7++/QJt8QX1epAgGOQnOBZ9y9e8v6NAr0Ojzv39/QMu0WSC2gFQxMv39x8Ano2Fronr+/ulTR0++jbE6c+rM689/dGyMDJX5v//6/Or1y38cPH9eXY729of46/+/fz9//v755NaDVz8Mxfj+//4N2vAEjiEmZhYmBgZBSfm07NzAqI+P7t44d+nm5fPnjl28z872b8uSxdYWJsF6Qn//Q50JdTyUBx5Eh9TPoCbDPzZ2dn5enm8//7Az/WNm5/739zc7v7AA2/9vP/7+/8fMycnMwPD36/efoJXnjKCVWH//MYkI8f3/9//nHwYWVlZmZsY/LKyCQvz/fv/9xcLIzMLOysDw59+37z/+MDOx/fr2ad+BAwePnT166sLHH6CxehZWDgbQanNQp54RFI9/fzLz6upqsv77++Ljd2ZmVkFJVWtj1TP3Tp07ee5DhvvNcxfvv/4kqm5obaDw88dv8HpsuE9AoTuKR0NgNARGQ2CoA1ChBsKg+hVaYqN6CbIPGFI7gCoasCxEJVgftOamLhtkKKgHDLKM8f/f3+/f/WBn//v7L2Q/Lcgu8M4aJtB6WUYmpn9/Hj59/vs/AwcT8++Pr89dvmfgosb4+x8Dw9///xj//PohomHibKHB8OcvqOvFysr07/dfJmERVobn4D05//+Dttcwgiu6f7++v/zyh4efj4OdgeHfT5BNYE//B20jAu1nQtSv4FYJyIEQDOoUQ1hQEuT+/39/MbLbOVgu3X7u2b0bB0+eu3Tl4W82fjs7K0FWhj8/wPtr/vxmE1EJdjJhYQBt7GFiArnv538WNRE20JQtqO4DbZFlZGT8//vn85ePz164LatraawspKJnrmti/vdv+IHVCxqnbvr78+v1Gw+Z9YT+gw/CBG86+s8AcjLoJDFIhIGcxAAKW7AT//9n+A8elAZX76C6GRTLTKCF42A/gxWBVMNuwvr/D9QVBu26hkiBtTCC9nSB+JCw+s/AyMHyd9Xkvo4FexgF5b29fcxMDfnfX23qW/SZWYyBAWwWaGCBgYGFmZWNlYmJiRW0i+nvTwYuW3vzpdvPPb9348K1Z7cuXX77g9XOykJJiOXHF0gFDLIF5nyIbRBvjbJHw2E0DYymgaGbBrAeTgEp7hhAJ2GBS2GI9yCitGaDwvI/aLIQbB0jExs7Owf7XyZQBQwWARfD/8CbVTm4Oe+d3DJ1yUEGVq4/f/8z/34/b+osY+02LRFmZhZeYXGev5dfMfLLZKXF8DGATrX8CTaAm4Hh/X8GcSkRDi7m918/Pnr+gUdV7g8rw7c7xwvq5wgo6Hn7BwSYSDAzsTAwMv398vUvKxMbExsnE8OrV6//MIO3FIF7wJANx6C9sKwsrMzgTa+wDuh/8HHQP7/+UTa0MFJaufvuxzULlj19/VVUTtPeXP3P918cnJwiwmLMf2/9ZeQNz4hWYwR18f8xgEg+Boa3P3/9+POPCdTG+M/w/x8zB+fbm4dyyibcff7NJYHBosTvz9cfH3/+4+HlMtBRZWcGNRAYmJn+MzCwsbCxMDP9+v3tx29mNlY2dmamd2/efP8FWgAGaSQgRx44MGAENon/oJVnMAlIHocph9LwaALV+f+Z2Tg+P7u8avvpX6xCCRn5TVFmzAwMx3dc//37PxMrVAfofJN//1mZmfl4uMAtG0YmJqafP/8pGpjrK63acePT1hVLnl97zSUoZmttyvb/73dwUCM0w1igBsAoGxQCsAgaZYNCAIJHw2Q0HIZKGgB1eLAWrqCTJ0ADkKCeD6gvBmKDe6GQ/iKIC8KgChxEgzA12CArGBnBs5+gHuf/f1+/fP385euXr1+/fIGiz1++/vz1h4mFjfHT48nTVrz+/ldIybCuLE6Eg+nLk2uTZq/7yczKzM5lYWLExcz45Nz+9nl77n34fP7U/sSY5PDUiq41R7//ZZDXNlAS4fn34+vmZYuP3n37++Orlcs33brzYMf2va9+MbGzsPPw8rEws3x7cWvr/ou///zas3nFtsMPuLnZwdUdqGPKycXFyMLG+u/vm5evXn149+X7f9AVDBD3g0Pi35/frHySTnaG7H8/37v3+N2Hv/qWVmpi7L9+//r5n9XS3EiIk+njk8ttHSsuv/5y79aFgszM4Nii6llbP/5mAtXzoH42IwMj05+ff8WUdTTFeNi5uS7tXNO7ZN/brz9///5z/8qZzmlrPn77y8knamao9JOBQUSEl4mdkeHH19079r778fv2uSPzNhz+x8HF9B/aFQb1g0HBCqLBi7RA7StQSwKMwRaCIhLEA8U5+JAsEAesHlzpwaTBqQAcTaCZZZAakM///Pj6/S8jNxvDjcuXrr79evXamZlLd39n4maCDeqDtIEUMzKBjv74D1rlxcD0//cvdn4pe2t9ToZvp46euPPik5ymoamm2Pcfv0HddPD4BNhekJtADJBVo2xIWI6Gw2g4jKaBIZsGQEUsSmMB0nSCkCyQlTtI/TpwGQySBLcvwARUiFps0CQlaI/Pv79//jGyfH9+KSs+BbmBwMzE8OHLv6i8itowo+mTZh648oKRQyAgMjzMQ/3t9XM9K06c2bZ6sbFepq+OhYe/16Fzq4/cWTWjf89K3j/fv7z78PkP63unEEH2/38YRFQzU4PutCy8euJATvIFLtb/Hz58/PKT2TchJcxK9s0fBlsb08Ubjn34/G52W/3yPubXbz8Li4uyfH79+w+oM/7vLwOXiLgAw79n/35tn9OzcgZTUdd0bw5mUMX45+8/cJAxMf3//ofRwsFWasXBV79//WXjd3Qw52T494uJ5ee338qWrlGeRyevOb1/zYIL+zYw/fnx7v3Hb39Y9Bz9eTmYf//4Azq2AxI1f3//55bILky9X9V/49XT+f1dGxbwszEzfv/y+f2nb8xc/PHJieby3B9+/FM3NlMQWnvhxbd9K2ed2rL447u3nALCXGyMb378Ae2ZAoUt6LytP6BjwkBjzqBzxf79+wM6ggvkZNCirf+gg8X+/PkDutYJND79/++fv2B50C4mkAIGsHqwH8Fcxv//QSJM//79/vVTUF7HWlN86ZGHlw9siD+z+9uHt4x8ouJ8rHfefHv9+j0jg9x/iHUMf/+B1myDe9CM/0EB9ZfJ3MZSYsWhTwxMv/8wm9taSXMzfvn0n5GZCbSuDBIO1Epjo+aMhudoGhhNAwOdBkDlK6gDDOoFgSpVDAw6NBHUvwFP3kE6TJCJPJqyGRn////PyMbBycfLw8PF8ev3HzSH/f/3j52L897Vg6v3XOXhF9B08It0Vn/+7U9IUsrFm8+O3X63ecVyK8NqXQnp0pYGpZXr9h658OT1Fw4+URtzx+CIEBdj+e+ff/xjZDT2jJrEL7Fi7Y5Lt559/82grKNv4+QRHezE9Pvnj7+MypbeTXU/5y/b/eTtFzYeoeTkHA2GB73TVzHxcjMzMv39/YtDTKOgIG7Cwu1vP30XEhQR5mX794eZl4f3219udham////M4E6r79FFfWdLTWWHbqjom9upSP14+cv8Kqif9//csSXVIsrbdi878SDx+/+c/IYahl6BwYFOOv++faTAXpiJqiaZGBm/Pn9h6yR84RJIhs37zlz6eaTlx///f/PLy5l5qTj5OrmZq35+/uPfwwMHJLajU2V0+auvHL/9X9mNpeIlCAzod7eOd9ZuNiYmUBHdbKy8fLy/uLhAh1SDQpmBnYOTj4+Xh4uDvCA9///jEycXNx8fLxcHODzpxiZuHi4+fj+crKzghoV/xkYmVl5eHn5fkH9yPCfgYWNg5ePl42Hm5Xh728m/syKctbp8w5devTzL6Ohc2B2os/+hb3Pdt+7fePmx//6rCwsPLy8Pxm4WUHuAR1vBprh/s/w/csPSXUTB2P5ZUcecQrKOFjrMfz+Ax5SADcCQeu3oJPINE17oJQ2ahd98/tomINCYDTMR2S+A4/1gkpW5E4mKD2AMeOzLz/ADDoToDL518+foIVIoPFSdNtBk53s7P9A214YmBgZODi5WJj///3HwMTE/O/3j28//zL8+8PMzsXBysTIzMrNwfTp4+cfv/8xMjJy8/FxMDN8/fYDcs7Uv38MXNzs/3/9+vjl+7//DGwcnPw8bN+/gVb/MjEygO4/4GL7/uXL1++/WTk4Bfk4vn/5+vnbL0Ym0OGIzGAFnJxs3z5//v7rLxs7Ozsb69/fP3/8/P3vPyMbOzsbuA4GNV8YGf/8+f39xy9Wdg4OVmboxibQWqR//xlZeLhYvn0BHU3BwMjAyc3Lw8709esP0FGYjJAuK8zvjIz//v5j4+TgZGH4+BF0yCXIZGZmXj4ediaGz19/MDIygXrM//6zc7L///3j4+fvjMysAoI8DL9+vf/0lQE8lc7KzAQ+ivIPAyMjOwcHeGvwf0g4MzExc3Cwg3q0///9/PHzz7//zCysHOysDP///QAth/7PwsrGzgZeFf/3z4+fvxB+ZACdrfHzF+hkaQ5OdiYGBmZWdlbGPx8+fvnHwMwrwMvO+P8T6FTOP/8ZmLi5ORn+/vkOPRqTHVQHgzvlbOzs3CwM/769rCiq2njigbpjyJzOTI7f3/8xMoPG+mFhMEqPhsBoCIyGwDADjAwMHP9ABzlg+gtcAYP6yZDqGdJjpzEbVAmARrVBi3MgVmFxFwNoCxAT6LYE0CKlv/9AR1OBO85MoGlY0Cwh6A4E0C4hkELQyRhgo0C3CPxjYAKPaoLWcoFvSgAdrwE6bQN0hcDfv6DTm6GV/v//oHsCWJiZmZhAS77+/AXfmsAEvdcA1DUFKWAGyTP+/we67YCBkQlylcM/kItA3TuQ20G9SpC7wE6CC4Lsh9z0ALoAAby8+B/Iff+ZoYdQgl0MHSUBs0FXEv7795+BhQVcdYICCTQ+/O8/A0gLKJpAZv7795eREXwRAnhk+D8jMyv48gOwk0D9atCsKgPIs6B2FwPo8gewhf9BY84gT4EDHmQXyHcMjAyQEAV7ELyQC82PDKCV0KBDu/6DzQStxvr3n4GRBXwYCGiJOQMDMzMLWMH/v3//ogXR//8M7GzM104dO3X16dO75/YcufLuB0t2U0e2txbs+Emw35HDYZQNHbKiS34ctWs0vY2mAdqlAVBn8z+uChjU44FaDapJ6IKhpQpoKhJ96BnJftC6nL9/f4NFQPUtqEMJWi0GmmKECYKTDRNopPPfX4hRoBVNoHuHwCpA63HBNwWBruv5CxYC1T0gXVA3gPvJoD2+oJqciQnUG/vzB2QSqE4ANRRAdzaAbATJg5ZdMfz/9wdsElQB1BxQlf0HtEEItPoI3KUDyYMYjIzMIGPhuhjB9TdEG9hFyAS4ImcGtTj+gRwBlgLtyAId1QUepwVNJ4BGbSEXK4G6yCAHMvwB+x5kJWhr9f8/oPXgYNeChMDhDHc/uBb/B5ohBpkOaoj8ZwDVmqCaHRTkIBrNj6Aa9z/Yd1AzQdcege7DAAUEeJ8SqJnzB2IFOJr+gPdPgyxnZPj/9z8rG8vzK0fqaxZxSEjxcnJYeASGOWv++P4LHPo4ggLkulE8GgKjITAaAkMc4C3hwBUwqNCkuyfBzoIU+bhbH6C6ElQlgCsekBNBupCcC+KCMHihGUgRTA2IhmKwPKgygBgGUwWShfQQQevrIKLg7iFIGVgPmAApA7kAbAC4qYJwM0g7WBysEioOEoRoApMgLmiBMXgxElgEP4GuHmI+2AK4RhAP1DqAhRu4XgS7DUKA9YBVg1SCGFC3gVokoFobHFxgOTCBqh4khKQeFCYQPSCDIOEEtgZNDdgQ0NZksOHgeh6kmJGR+f+PX3/1nPzau2S//2OVVFBztDZkZ/gNvvwKdRAeasEoNRoCoyEwGgLDBYDLQnDxiMVH4AoYtoEEXK6CahuQQlA5PMqG1XGQ8BsNE9LDgYmB6e/vP0IKOslqOowMoDPOvn77+Rs0Ug5aiQdKaaSbCdI1Ghej4TaaBkbTwOBPA6DSCuRMSFyBeQgCMgcMmlwFdVfA4hB1g99fo+4cMnEEGhj/9w80RA+ZkQaPm8N6v6PxOGTicbR8AIfAaHyN5lki0wCo98sIOg+B4x9owg6cfFAIaAUMFQONf4JHEEG1MdgK6EjnKBuc5EbDZzQcRtPAaBoYTQOjaYCUNMD4H+ciLMbnX2G34UIr4VFqNARGQ2A0BEZDYDQERkOAKgDUDeb8i70HzAKqyEHHUoFsAvdzQWuQQB1gkMAom9ihBvgypdEwBI8VjIbbaN4ZTQOjaWA0DYDWUUEOIAHXqOgEvAcMkRitPkarj9E0MJoGRtPAaBoYTQPUTAOc4J2ikFoWmYRXwJDgRpYaZY+GwGgIjIbAaAiMhsBoCJAPwAcz/cc5BA3qIYMMh9T2INYoHg2B0RAYDYHREBgNgdEQoByAa1Ywgc0s0BlLSOLgkwih/FE2JCBGw2E0HEbTwGgaGE0Do2mAkjQACT10kvH5V8gd9ugSo/zREBgNgdEQGA2B0RAYDQHKAa45YCbKjR41YTQERkNgNARGQ2A0BEZDgFQAqoBxjk+Tatio+tEQGA2B0RAYDYHREBgNATCA1K0QEiyAToAq4NEF0OihMsofDYHREBgNgdEQGA0BygCkboWQWE1iwVM5Y9UwKjgaAqMhMBoCoyEwGgKjIUAMgF4nh0MpC57KGYeWUeHREBgNgdEQGA2B0RAYDQGiAJ5KdlD0gPG4jyj/jSoaDYFhFAK0GJQazWLDKIGMeoU0QIsMRZoLcKsG9YBBZ0XjVkFnmcEcWHQOilHrRk4IIFeQyGwqhgA8m49mMSqG6qhRgzMEIJkIkuYh7AFxJ8QBeHIcuAcMu4xhQJyIZikosP7/Bx3DCWKhSY5yR0NgeIUANINCbnejrdfgc1GgjDWaxWgb2KOmD1AIoGYoeJofINeArAXlbVCWA7ExMQtICLc0SJbGGGw5mIBbBGkwQEi44ChjNASGZQiA0jnoTjIkz4HyLBKXUiY4d4EIEIYYBrIUdgotRGSUHA2B4RECoLSNyFDgNE/lDEVSOEHauri0gCtgXJK0Ewe3U/4zgCADI+hu4////4M5DBAx2tk8CkZDYLCFAKjEAGEGRgZGMABlBhAHjKGjaaQ6ejSLkRpio+qHSwiAMhMIg/IP1TIUuYEDdghOzeAKGJxXcSqhhcR/BlAZA3La/39/GRgZ/7MxMbEwMUMaKiApWlg6auZoCAzKEGAEp/v//xn+/Pv3+9+/f/8ZmUBZg/H///8QKdJdDWrVgvIRyByGf3//j2Yx0sNwVMdQDQFIrkFkqH+MTOAcBcpQoNqZ/hUezpAEV8DwkXJwdx3kQgZQTxSkCZyBoW1wqrDBJQPEwP///v/7/5+DhZmbjZmFkQkabCBbR/FoCIy4EABlvv////z79/X33x9//jIxgdrv//+B1kOAChSQNGzQmBAbVPWCB+H+////799oFhtxaWnUw9AaDCVDMYIWO4FHhMEtXprVcQyo+RTOBTkKHTO+oPNlDIwMoDKFAdQD/vf/Hw8bKw8raCU2pEhBd90ofzQERlgIMIIz75fff778+s3EyASqehkYQD1i4nPIaBYbYWlm1Lt4ABUyFB7TiZbi+PsHq1pwMxvStYVU1LRmg0bVQPO8//7942Zj4WFj+QdriGB136jgaAiMqBD4z8Dwj4GBh42Fm40FNBrN8B/UWgc326FDUwTzKSSLgfq+/7hZR7PYiEo+o55FB+gZCpw7QMuOIDUzjes7UOYFN6nRnQXjs4AqQxgHMjIM5SG3uKnBBo27g6Z+Qcb/+/efhYmRm5X1H7LJIJlRPBoCoyHA8O8/Azcr68/ff/+AJ4QhpQQoB0HCBjnXILEZwesYwWPPDP/+g7MY22gWgwTZKDmiQwCRof7/Z/wPyk+Q+heRexAs1B4hZeIQ3RASawRA9wGjVL1YFVIoCJr6BQ/Lg8fH/v//z8nGysTI+O8/ODAoNHxU+2gIDK8Q+M/AwMTIyMnK8unnb9CELhMjOAOBB6Rx5WawClDhMZrFhldiGPUN5QAtQ4HmdMC9YFAPFVeGotxWiAmIhjOEj0KC5l9pXvtCal5Ghn//QCzwUrT/bMzMYAY9LEfx8ShnNAQGfQiA8uz//2zMTKBaFdRmZ/jPwMjEBCozcLodvJQS1KAFLW38z8gwmsVwBtWoxEgLASwZCrTAAm+GolYY4a3g6dwDBvkJVO/+/w9eFg7igoYDQPQoHg2B0RBACQHQEixQ/xdctYLWLTLga7AzgrYtQUqU0SyGEo6jnNEQAAOUDAWaqgFtEoZkGbA8bQhQ5Y/TZFAPGKQAUg1C6moasCFGMsI7wTD3QCyE8Ubp0RAYDQFoCECyDDiDgCphJtDN3SAp0JwNWBTUqgeNKIEEQTuWwGNJsCwG5oBlIGrBzFFiNARGbgggZShQnQvLUKAxI1CgQPIJsiJqsEF1K/5FWGBbQI1nkCMgGOIUKrJBdoAmsaAzvqDhsX8gMbwug9g/So6GwIgNAVAeAbXTQaNF/0GDRqCFI4wgEXCQoOVTRlARA81i4IoZpH00i4GDapQYDQHI7gFQyxScU2AZioERLR+BFIIxlcQhtoFNxEJA2tWgzf6g7AtRAMrmEBY4+0LyMcT55LERPgE7BuRjuEEwi0bp0RAYDQGsIcAIzp7w/AnPOmj5FKwAlMFACv6DVmNhNW1UcDQERnQIgLMGLJ+Aaji0fATKPuAAopY4yEawgdgIFnC2hcnA7YZUtzBhitWAl5KA3QEaTQNtRgI145Ftg1s1yhgNgdEQgIQAqKj4D2qhg+Z0GUGbjMBtV9B5PhAFIBKai6BZDKwFtBsJcrQ6VBKkbhSPhsBIDwFw7sCWoUBrHJECBznbUIWNbAiSPQwMDKB9wKD6FdFJRZWnCg9UbECqdFDRQBUjRw0ZDYERFALQZViQfIwtE41msRGUGka9SjH4z/Af0QnGlqEotgFhAGQeGMFHYcF6wJCsjSJFVQ6o1Q4aSQPP/YIO96G1hVR1/ahhoyEwQCEAyiegdSKgeWBQOx3EB7WYMZ0DyuegnAXJYtjVYOoaFRkNgREVAqAMRFyGol6wgDYv4DANMgeMQ5Kqwkh9bFBzHYlLVWtGDRsNgeEWAnjyLy6vgooZXHKj4qMhMLJDgN65A09lB7oNCdR0pmWEQMyHkGB7QDspSAoD0MVSSJ5gYmRgJkL/n3+gKXbwTmewtQygA8n+/mNgYaZ7DEDtH6VGQ4C0EAAnc9CCCcgUDoREygpQ0yCZC0KChUjOYmBdDH//gfIIIyMDM+iQeIgYOvn3H2gujYVQ0/3PXwZGJqLyKboFo/zREKAZgGQoiPFgNqguwMxQEAWUk5AsCbEIq2mgCph21kOshJgPISEOAk2GgypHiDxhkpmZgRVV1W/QLcKoQhg8DpDnGEDVMEyKjZmBgRl02j32mylgykbpUTBIQgAy+QvJwBA2JB+hOQ8iCCHJy2IgAxkZ2MFZBsT+z/D7H/jcDxAHBUPUIGcruDRk5RdotJyRgQOcY7Eqg6sfZYyGAJ0BJBMRzFDUchUkS0JIrGaygKaiQS1aiJOwqqFcEN0BjNizNhaL/v8HNcY/vGaYepHhJ6ixAtpeYaX+30uO4ddfhr//GZhA9SkDC7jB/ucfdIc1qIv8n2HuAQYGMYY4dVAdzMjIwMbMcOEOw7r7DBmODBLMIO2gO2YYQXoZGKBqGP6D7qJhYQT1A/6BrQaVJljcNSo0GgI0DwHwPghoxoRQEBJb2xVSsCAyGlgvsS4EZQRmBqafDMvPM175yMDP9z/aiEGanQHSzEXJVgwMK44yvORhyNFj+P0H2sf994/hLwMDqCgBN5S//wEZVbiD0Vzvf5QKyBBQg/s/AxOsQ/znHyQng3IZMxPDaBYjNp5G1VEGkDMFJCvRq/qD2IbF9SywFWCIrItFFZWEkOxAYuI3/D8of354zVC3mZGBjQFU3/5mYDjIuCj5f6wiks5/DH8YGSBdXgYGhl//GJj+M0zewchgxJCs9Z+VmeH/X1Cpde4WY/N2Bn/b/5IsCMWQRjpcL5KhICaoHwCiR/FoCNA7BMCZBFKzQtvIYBHwKRtY3AIdqYbIgOo8UJKH8AiQ/xkZmH8zpC9inHOdATTW9I1x0jmGQ0n/FdgZ/iJlq5//QJLz9jOek2DI1f8P7+OywfvN3xmqtjNkBjHI/mcQ5f0vwAayl5GJgR02Xg2p0dHyGiQDgpSO4tEQoCVAzhSoWQnGo43tjKDsi91o5BlS7CqoJApaeAV2B862AB6L2FkYWbgZJqb8z1Ji+PyGQbOTceM9BgcOhnnXGBT5GU+/Yihz+y/7h2H6ccbLnxictP6HqDIw/GUQ4mVgZGaYd4Txypf/sWYMhkIM7GwMrLygGp2RgWH3eYbVdxi1Vf7nGDIw/maYcJBBUZrx8WOGx0z/M4wZzl5lPPbuf5gZg4UIzrE4PA4elRoNAcpDADmrgNmMsAY7HrMhrXzQwXNgLXhUQqX+/wcNDn1+wTDnOkOC3//5DgyXLzNYLmdcc4+hTJvh11eGuacZL31kcNL+H6LC8P8fgwA3gzj3f8b/DBP3MsipMPjLMxy6yrD7LUOODmPWKoZ1txnusDE0WDEoCvznZgVv9vjOMOMU2ARwxvzzm2HiPgYxacbv7xhufP4fZc6gLwDKYqP9YGh8jFI0A5C8AW6XQhc2EplHaOYiUEcRZDikaUAjEtxiB5kNbsyT09b4C55c2niGoXEfQ90hhqc/GDTEGZ49YWhYwdh9nuEfMwPLFwafmYxt5xlY/zEkz2PMOg5qyLOxMBy6wnDmA8PB84wucxle/we133//YeBnZ9h0gCFgO6OsyP8Vuxlj9oAGx/q3MQasZrj5hWH+HkaTSYybXzAcuczotpDxyT/QGDU5jgaF6ygeDQHyQwCe6iDTuuDrGEBioLwEmooBtashbHAWA1kE3t8IFwOJEIP//Gfg4GewFmFYfJixYCfDNSaGS5X/CzUZfn9l8J/F2ArLVpnHQGPOf/4w/AEfD9K0mXHZXVBzdt9FxpYdjD/ABRsTG2iRI8M3hriFjLNugTrWntMRJmQfB+Wm/h2MUasZLr9nWHaQ0Ws1w1fw8DXIY8S4dVTNaAiQC2BpDHQYMoQNJyF5huokPGPicjK0B0zrhgC4BAE1OiAexuUaXOL/GBhYGRl2XWDc9Q80Cu3vyFCgzXD9BAOLIEO97/8QCYa9Rxi3PmbYVfvflZ9BlZExdydjrdF/hj8MGioM03z+v9NkFJ7CuO4xgzx4EPvXd8aZJxh4uEAT0VysDCv2MzZYMohyMKgY/J/szcD+irH3PcPCwP+nTjKar2W4/51Bhgd0kSIx665xuX9UfDQEyAgBpFwJ6ftCp06RxBGmgrMYSAE4i2FVglCMzILc38DGzXAg93/FNsaZRxgn/mAQFWXYkMrAfJth1xOGnXX/3SDZahdjs9l/TiZQxc/AwCDOx8DPBlp1wc3BwMrBICr0P02Lce19hh7P/1KfGXh4GBX4GS5cY9z7lGFnPcKEMgMGEU4GFcP//T4MuoyMyacZ3/39z8MMWs+B7KpR9mgIUB2AcwW0GwwZTAKLgHIN1e2CGwixAs5FY8Bnb9DEacQFFw6km83CwPjjP0NH7P88VYYffxgEOUFG/PjL+JeZgY8FtH7q8VcGZnYGaR6QuLooA8MPhnd/QX0EET6QCA8XAwsbw7vvDIrg1V8//jC8+8kgLMjw5iujnhqDvg4DFwPDj78M8uygbRiffjPw8IGKlS+/GZiYGX78ApkwikdDYEBDANI0x5+X0RxIbF7795+BlYnh9n2GZfcZykL+94QzvH/FYNTHWHecIY2HgZmNQYYbZLK6GDRbMSNN6DIxgVqxHMygPPjnP8OnX6AO8dt/DFL/QFmJkZHx2SdQxpRBzZh//zHwgfMaaAUWzDSQHaN4NARoDsDjsCBbiM0gILU0w5DkT1LGJsMtYPPBBFgzggXmEkH8B62iYmVh4GRl4GVn+PEb3AYHC/4HL2O2Ufz/9zvDjJMM7z8zdB9lkFL5r8oB6isfucxw/i3DlqsMf34z2Mr8//6L4f8fBn7O/4YiDK/+MXR7/ed8y3Dkw38Zzv8//oCWbjEzgWa5fv0BFSsM/xn+gZduEeG+USWjIUC7EIDlFygNpVDtgwhCSPBYMKo0Ph64ROL8z9CwhlFnMmPDfobO4wwPfjBIcv+HZqtT4Gx1hEFS5b8yG8PX36AhaAZGBrZ/DOeeMjx9z7D1DsN/ZtByLSG2///+MDz7CKqP//1n+PLrv7kSSsaUVvmvyfn/y2/Q0mhmJtAqaFAWw+e4UbnREKA2gOUSWD5B8KltE8Q8fOZDKmBatwXA5oMJsIsQLDCXMMHIzMDLCRqF/g/bnsjIwMDCAhJkA/tARY1hXej/fYcZDSYzfhZm2BQCWjHNys6gzs9Qvpox+QhDrt9/G26GH4wgLd8ZGRqD/5v8YZBsZlz5jqHFFtRX5uFg4AXvXOTiYBDkADkJYj50jB4kMIpHQ2BAQgCWX6A0lEJ1CkQQQoLSM6osPh4TE6g6lFFiOJz5X+4HQ+MOxs7TjIF2/5tNGaRkGdaEIWWrYNAueg4OBj5mUNlV68pw5zKj/xrG35ygQakf/xm0lRj0hBniFjAees8oywdq7ApLM6wKZUBkzGAGjv8M3JwMvOAF0uysoPyIr3xiGAWjIUBtAMslsHyC4FPbJoh5+MxnfPXtJ6i7h7KFAaKNeiRoSSZou9N/hv///v37++8f479/orzcjKBJYaJs+f+P4ctvBnZWBkh1C9Hz9y/Dtz8MXGyg03b+M4CG0X79Zvjwk0GUBzSm/+cvw9dfDBzsDIx/GL78YxDiALXKf/8BDTVzsjFwMIMC/81XBm4uBk4mBohiJmYGThbQmPNvBlABgWw+xMZRcjQE6BkC//7/f/P5638mJmYmEADNWoEmsEAXH6E7A5zFQBn9PyiL/fv77/9/UBZjIj6LgXMQJM0zMjLwgCvIX/9AOQ4lW/1j+P6L4R8jAzcraDnVh+8MTCyg+vjdTwYedpDiH78Yvvxh4GZj+Pkb1ERmZ0bPmL//Mnz7BZrc4WRhgORHHnZQhkX30Sh/NASoDf7////q81dGRmiOYoDUQOC8Q22rkMxjZGT8/5/1D/bDn8AVMCL9g7IwqHEL0k5FNmiBCLiKB5UOf0Fn2f0TI6UCZmAEbfP/9x80ZgVyGhiDDsxjBE01QRz69x8DKzNoCuoP+Dg90FkcTKDFUwzgcyt//QVJwbX8Aw+7sYIV/AFvNQbNbP0HrQRhYgJtNgadFQDWCHIs2LpRYjQE6BwCkPKCgZGJGVwDM8AKDFDjEeQUSNcRkvwpzmIMoMTPAjsr49dfkAVM4PyFlq1A877gnPIPvH/pP5jNwgTKif9gp22ATsgBtxP+gWVZwadtIGdMBrA4JD+O7gMGhfUopj3AnaHgq5XR8hS1xP+z4aiA4YuwINkYEga0YIMqeYi5EC9CbCKW/M/wG3SXEopy0HA0kiAzE6gE+Qs+/xmypxCascHVNkQErgXSMfgNnuKFSEEUMzKAyhGwMGgdFqalKC4Y5YyGAC1DANxKRFgAyTigmhYqBslPIA5EEEJCzosGiZKImWEHwDEwgFqrEN2Y2QqUxcCNdCZG0Ng1A6hjDtrIC+mcQ1rJjAzgeWKwMmZG7BkT5Fps+Rpi7yg5GgJUB7gzFDwrwRnwqhfiCjLFQYkcnAsgpmCSoAoYrAiSuyHWUJkNNh+8bApsP8QOMJMEAuImNA1ogiAuCENVITGhImhlE6QahsjBFcMZaIohykbJ0RCgWwhAMg7cOkjxgZR9IEkVJADCsH4xWskB104MA2IimkqQIAhDhZGYoMkriChcEJMBzUdwCdTyCEkYYtIoORoCtAK4MxRcBpIeIfmJCmyIQRASq69YIAPgyFURVnWUCIJOmoXpBzWToTkSJjRKj4bAaAjgCAFIxoTmGnjVBSkZkLUgVc5wxcjyo+zREBgNAXjNA88joPwFyTsgSdpgvOazgKdmGf5DKKgDkCts6rAhpkA2M4LcA+FDrRulRkNgNARwhADS0VbwVjpsNAk1F4GWOYIMgYhCSBB/FI+GwGgIwACo9gGPFUEzCIRCVH8QPkQ1tdgMoHOYIUZikKArTECCoJYA2F3wRgLEdkhbmxpskM//g8asQFaBrisFWTuKR0NgNATwhgBopSZKgx00xAzOlmACWhmDKmcQBs3IglXjyfN4rRuVHA2B4RwCoEwDPo4JxAANKYGXMDCArymgWd0HOhIdR6CCbkNiZGBE6QFDqluIBmqwweaDandQDxiEQUUIxPhRcjQERkMAVwiAMx+oCwzJNKAKFrzqENrXBUtD9DKCLkcA8UGHpkNUQyRGydEQGA0BGADlEHDlA8om4JoXnLsYoRkKLAVTC6qwKGeD8yxomhdhFCoL3AMGOQXcIIA4EMxEtKzhziJbHFQDg4oISNMc1AgZ7QGjRsMobxRghgA4w4EzJ7jNDm6gg7mQ7AOWhudTcAb7z/gffGo0RArTxFGR0RAYwSEAyhawSgg0EAvrAYMyFY3qPtDeQQY8PWDwOVIQu2kXMSDzQRhcWIAZtLNr1OTREBh+IQDp1IKyDgjj9B9IEqIUtf2OU8OoxGgIjLAQAOURcJ8SllEG1v+gbUighgHUWWDHUJ0N6oeDMKjBAbIMbMsoMRoCoyFAbAhA2+3gHATTg5xPIWKgTAZWCeKCmvsgehSPhsBoCCAAJF+AswmECa6OofLIeYpabKjR2ClwDxg8toWoGcFugyqnFhtqHLKfoEKj1GgIjIYAoRDA1lxHzptY9I/mNSyBMio04kMAni/gDKRmLXKeohYbb4jDKmC8iqggCRplh9TzoIoeMv5OBWNHjRgNgWEdArBCAEHDW+3I/gYJgjBUDMwEE1CBUWo0BEZDAALglQ80g0ApiCRtSLiVmMaDh6AZoBuVIE0CyAYG6rIZEOvMwC0QiOmYzhkVGQ2B0RBACgH0jAJatQmVRs6nIGUgDJVCYkJFRqnREBgNAQhAyx2Q/bEQQeQ8RS02aIQbYjrEelSSBbRxEHWVFrJiqrAhM1Ogtd7gdjyoxQHCqA4Z5Y2GwGgIYIQAtO2MlHEguQmUq5EUQwRHsxhSkIwyR0MAC8CTodDyFNXqPshCaCxuAQkxIfdNQZ1TuLXIs06UsSFGgkiwOWACZPcoHg2B0RDAHwKgDYuQggE2hgTPR1CN4OwEEoQsfIZxEWf7QNWNUqMhMBoCoCt2QJkFlk0Q1R9YBBpA1GNDsiHIRqjR6BToLGiEGHLHlDZs8C5FhIWjrNEQGA0BPCEAabCDcg04P4IJpDUj0GUVSAaAdwyDlIEwkvgoczQERkMA1hlFZCh4NoEzQIqQQopa4khGIjNZQCPgkLYzsjBV2aDxMbCByA0LsMAoMRoCoyFAOAQgGQeSjyAkmh64IEQlmuwodzQERkMAGUCyCSTXQEhkWSqyIYYjV+JohrNA5CAkmhwVuXB3kG3R/////v37z8jIyMREzsrt////gS45BVv//98/BkYmSN+CQj/+///vP8ys/+DzPEk1F9kEiGMwRSDiFJAgHzNB7j0myxSQftQQA/sVekE8wkiwKOjwYoQQgvUf5DHwGU6gRh8jsnuQYwehAcoCWY6sGCoMo0DJAtQnRDEQJkmA/v//HyMj1uQE8gkujxAwlAbS4ClgkLng9AvaRwDioGIKsxgslP+DchkouEEM8vIaqruowPv37y8jIzPoQD9QtDCQmsWIcgEocTKA/I1QTf0QgAUywg7SWFgcScgAvFkLWTN5xReyCRA2yEJGCsoaugBiMhS1HALJs7hMGxo94P//GViZmRmYQb749///33+gQXVGRgZQTcrIAIpycKH0H1zHglLpf0ZmZiYwA3QXIhMjIwszSDNEIysLiP3rz19GUEL5//cfqBRmAt9GzsTECMp2DAyMTExg05lAxoMm4f7/+/+fiQmU9//9+weWZ/z79x8rKwsTA8MfsHtYmEHJ7ufvP0xMoMLi37+/YLtBmfrfX1DbgRnUdPiPzEYzgYHhP1zk979////+Y4A0OMCtDzCb8d8/kDP+gdwMOsEbZAWo6AU1A0COhzRTQA5l+PcPlKdABjAzsbEw/PkLPkeYgQFUY4H8wswI8iu4AQHXycDAxMzMADYEXPiCAoORiYkNFGL/fv7+zwT2IwMDAzPYM7///IUFCyiZMTIzsTAw/AbZ9P//f0awYpB/QRU1I4jLCopAKP77H+TZ/yCXMEFi58/fv5DSHxQXOBwP0QwOD1A4MDAwgN0GEv4DCm+Q00FtNFDagHkfFC0gBaCIg4YXaLUDMxMzKzPz33///vz9B2nYgTSD3M3EDPHIX7CJ/xlAHgFbCblLBJQIQObRDxNssIOSANg5EJVgJkkEIysL8//////8ByWVf//+/2MEMf78/QfOHaAQhRgHCkNQ4IFEwMEFqrRAAQ0OJdCtauBZa1BrCBQFf0HByczECAs9UKIC5V9ogEPMBEuC7mlhZmICmQ9KO4zg9AWS/8/AyMYC2qzx599/SBaDZDcGcEoGzemBkjgos0MSDyQomBgY/v2Hug2iEimKGZiYQOrBaQ+aVRmZWdgYoRkZrP4vAyMLGwvDX1AuBKVFpEwN8hxYL8gQUNsP1CwANSpB5QQDA8jlSH6E+AicBZhYWZhBYQvyFiST/QeFIzQfQtmQ7An2EyirgooaRkaQICMjCwszGwPDz99/ILrAxjCAyxlGJmbG/+CSAVoYgss+RiZQSmZjYPj97z/ILFBeY2ZkABVl4EFPUAECNwoStqBi5x+oTGBmghY1UANB7oW4EGQSaCgX7CqQ88DxC0knjIyMrMzM//7/BxcM/8G+ADseFL2gYIe4ecBJSDaBJBUISSMnQQwHFY44LGB88/0XDikqC4NSKTjP/P37l+H/fxEeLiJ7GP///2dlZrx75fSl6/dZRBUdrUx42EC9lj//GVgYGUAdWySXQvMByKL/yH225w9v/uNTlBJg/c/AcP/auX+CGqpS3AwMDH8ZINU61Jzf//6zgstrcD0HCr2/DIyg6hpmBcRSuMa3T28//8GjpSTBwMDw9vHtl/8EdRREQfUokq6//xmYwTHw/z/IOsiYA6jiYmRkYWSAm/Dn339mZiZmBpDIsx/c2spSIE8yMIAqJSZQxQbJ3ozg+p6FCbRIHmwqzGXgbAZxPAOIzcAK0c/A8Ofnx7sP3iqrKzH++/ufkRniAAaQOQyg5gM4rBiYED1BeBjCjb5z5SyHjLGMAAOk9cPExPjuxaPP//mUpAQhakB1GBPjl9dPX/1kV5UVhQiCyhqwHyHcL6/uHb/6UExI+O3LdwIyUirKqnzsUB88fXCLgUdOWoQDovL3PxyOZwB5Cdwag2pkYGC4fu7I4++ssmJSKqqy8AoexQRw84gBySUQW/79/n7r/mN5FTVOkKnQBACR+v7p1cNXv9RVZCDW/P///y8jI6gSAEvDKwAwj4bE////33z5xsDIyAxq74BciT/LkJfF/v9nZGb6fe3cFSFNAwn2z4f3n9d1sOf/9fLs1Vf6prrsYP/BvMwISTDgxPOfBZxT4KkFVA0jjSfAo+D/f4Z/jLBcBqlccZgJzymg/PUPtH7lPwMDC9Ofk/t2C2g5qolzvHl0+9kPTj01GXCDlgnJMf+YQQ1dkLmQAgGSr//8A6VwuAtBVTiqmxkY/oPdycjw/fWZW+/19VSZwI0PUN75//HcuSeaRtqckEQAylPQwgFkDTjXwOyECEBJUCufCVpowG0Ey/29ffOeqJIqH8v/v/8ZQVaARZHVwIsXUAjAyg2IXxgYGL6/e3Tt2T9jHQVI+IN0o6bqv//+w1rIIEkGBoavb589+sqqISsKH/CDmwZVwcDw++8/ZibGd09uP/7Goa8uB0pnsILxz99/zKgmIrsQYgJoZIsR6l8GBoYHd25yiquI80JLTXiJCmrWgJoFEE0DQ5KaoajoSpbfv7GaBi9VsMoOvOD/f/9YmJnvnN2+5/q/AGfbW+cObj/A5qTL/+gzu64867mr7xQU+M6eOsspKMfP8un+63/mFroPzh5//kfAzd3q082LF+4++vyb29nN4tC6hS9kvLNDrVkYGG6dP8FnpPD9yWkGCRNV7te7Dp0TUzPTlWS69uSXiZ7CtXPH33xn1zfXf37xBIucufifx29ZhZme3Th65aG+naem8L8DB4/84pWy0BA9fujczduXhI19tBXF/zIwfHp2Y93Be/c0FTUtPWW5vx86dODtDyZVAytNaeYjew6/YeRzdLASYPq0f/fxL9xi7g5m/z883H3g8u07l4SNvHWUJRkYGX++f3To0KWbty+ImQXpynNuW7/7j4iCu4MZ0+enW3Yf/S+kZK0ve//+O30D9YvnLkgoyT6/du3B28/i0jLfnt7/L67ubKb59Nb501ceyRtaG8qzn9p38uH7T3La5orMd+fN3R1fVqUpzP73z88LZ4/eefZN38ZJSeT/kV0H3jEIOrtZ/X1x68DJG/+YOMycHJif3z157bayqS3/xzsHTt/Xs7P7eO3E6YO3FOXELZ3txDiYGBkYP7179YH538OP909efaCib62nIs7EwPD1w5t3v7gffHt19e6tn/+FnNztef++3b/n2BceKXdrzc2L599mlmZ680RQy0yDjfWfmsrtc0dO3nplbOvA+OYFA7vsmwdXDp29o2hkg+n4hLJqDWG2P/9AjRBWZqbH188cvXhPy8Zd4c+dBcs2aWhoHNqxM66kSujd1UPnHyjCvP/owxcJZX0rPcW///8z/fl+8fK5p0/fc4lJsX159ppZ2tNK48WLF0JC/CdOn/jEIuJkbfrh0eWTlx/I61trC31/8fYTN8Pnazdvf/rN6eDtIvD9yabdZ5m5uGTUdXUVxcEVAKxgHvhcQpEL/v/7x8TM8vz2mVecCuLSr/bt3smla6X07urZ2//0NMW27D3CI6VlY6rJ8O8/0/+fl85ffPb0KaOQsrOF3rWLh24/+mVha/X08v5bL1lc/N2Y394+d+Xe1y9/9BzdFfgYT+zf9vAdu6OPs+DvN/sOnvzOLuRoa8X65cm2Y2d4JTRAZv79w8zIeO/ymct3HjMJyrtaGV67evLxw1f/BOWcbY3Y/v9jYmb++vLOvp27hT7yawRav7t/6fR7MX016T9//7Ey/Lxw+cyzJx84JBQdzHU+PL62++hlOV0bNZFfT75wqwp+P/vgh4WB0u3bj0SFeI4eO8IloeNgqn7twqE7j3+Z2Vq9vnn8zsu/5k5uElw/ju3ddffevVcMSvr6av///2P+//PM0f03Ht1+/kZEz1jr4tGd15//tnT1lOdnOnVw+703DJY25h+fPZLTM3h//Twjv8KP9/ce3H/MIijD+/fd429cvu42/94+2nvsLLeEloOp+sVzR54+evVPWMXZQHjZvDkm4TneRrL///19cP3C2asPpTRNzbRlH1w5fe7WczUzO00x9qPHDj17+11O3dhAjf/ogSM/eKWstKQO7tnPIKpsIP5v18YDd2+rKqqbGWtK/fnHwPjn++mTBx++ZTG3MXj18JGmsdGbm5d+8kqzfnhw4upDfVPbb9d2LL/A3FoVd+f0oevPPls6+vD/fnTm0q0v335Kycg+untL3tBRX16QiZHx7f0r5z5IGqgK7tu6/5uArLOJytUrd3RNDd/cv/aNR4rj3YNjVx5pWdprijAeOHDkN6+UtgTH6cvXGZgELN3s+T/d23j4Gisnl6a+7o/3b7+y8jy/cOv2qw8SygY2BgqXT+y/8/Qrr4S4iYkJHxsDbCSOokQ7bACkuTN4vfOPgZmR8deF849NXF0kJKXsvMMDnHTe3Dl//taz/79enD5749mtK5cef9dWl9q986CSrualfRse/RXk+nJz864jVy4ef8sgI8n0bN/pO8raukrSkpBWGb8gz/m9a+595pXn/bxq1Q5RKembh7ccvX7n9rUrL58+uHb/zdd3D27ffnj68M7TV+7fv//g1vkjey68MDXROr5t+7U7V05cfmaoobBz/TpeFV15cV6G/yyMTEwsTExszAxsgtLa6jK7t6x//vazkLSSJOvrvfuPnj64/+obRmVJ/l/fv+7asPYLlxjDs0ubdxzduXsPn5I2yAQGUDOI7f/3rRs28anoyIvxszP93rdlM5uyvsi3ezsOnNm/dy+3gqECP8PzJ7dPn7ryn+HvuZOnXr19eODQVXUtxX2bNouoaD69cf7mzUvbDl6UkJY4unnNpZvXj564pWOofWbvzk+sIjqa6sI8bIyMTB9unNx+8r6Kstz/X58PbFv/7L8Q59dba9fv//Dtl4Kqxo9HFw+fuXrz8pEPzAoSfx5uOfHA3NLw96c3X/+yyalpizE923PoEmh4m5Hh/atn1y4f3rvvvJSaCvvvr38Z/jEyMnx88fDJ85cXTx74zqfO+/3O0VNXDu/c/IlTnOHJmY2Hb8lpadva2Otp6Vqa6bx9fP/6xSMnb7w3N9X68e7l4yePb509vuXQJSkcjhfiZQUNhf0HdXQ+P7q48fBtU1ODM9s3PP3Fq2NoaOdgoamhyf7+wdpd51BMMNA+v3/bs6+gvhrTn697du2TVtO6snfzNzGVf48uX77/4uXLuwe2rH/0nV9BnO/T6wcbtx8SVVTlZPzx+e3LR0+fXDp75B2jrATLqyNHzxw9dJhbUYvx1ZWDZ24zgcYMQZMggzfbkOIyRvBgioaWyqeXjx88em/pbPPh5tX77/5qq4nuWredQ0Lm2bkdO04/YGFiZPr/89CubezyugzPzhw8feXckRPC2vrPT22+8UXMQJFl29ZDt6+du/D4u66y4O6dO07s3Xb/u5i+AuuunbsP7t766BuvvCjP948vNq3fxiUuBTLzzAMWZua/f359/s2sqal25/jW87dfnjt8gFfN4Mftw6dufQD1+xn+nz973SujSO7X42e/Gbi5uNjZQMmYk42Fhenn7u07xDT0vtw9uf/U6d07T2qamLy8sPf4pZu3b997euvs1n0nXjx5ePP69V1bd3CKSj89t2vf6avnj54Q1NJ/dWb72cd/JTm/bdy65ciebc/+S+mqSjODy0JmFuarhzZf+yBgpCbHwcd569TBcy/YTDTFdu3cfv7ovpsfuEy15d6/eHLqxInP/xjuXzr+6Om7kwf28CrqPjqx7SWbNPfHO+ev3tq5eRenmNTTczv3n7l64ehhXlWDbzcPXnn8R1dPW1KMH9QX/Ppkw5YDIooqPMy/X9w5uePYLSlp4d2rV996/pZLUFJVim37tl0vHt48cvGJgYbCvs3ruZWNZfkY3n76zi4oZayncnj3tve/GFmZGR9f2Hvw8htVJfF/P348vHzx/rNXV67e/PLp3o7tx2TUVZh//xAQU9DVV392Yd+Rm1+khRnWrN9899r58w+/qogwbNx7TkNd+uzhwz/Ag1+MLJwCnP/2bN3wlUv835OzWw9cuHXt3KM3n29evXbrysldx29KSwvvXrvxys3LJ6881dFQ+vL5m5KG5ps7J48du3Dw8GEpNZ3P906euf74zdNHT57dOnTspo6B1uVj+69fP3fy5kd9Pdn9W7a/+8UAGoAYPlmHCgCc6KhgDs2MgHUzoGdZMoDmk/4zsfHy8LBycfPxcDIwc8gqqQoK8HJLyqqJCz1/9Pz335+s/DLqclJM7Nzq2gaailL///zh4OMXFRKAGPb/z+9bN+6IKmvw/3/58M33nz9+iSupaqroKggznjp9QdbA1tpQ+calS8IKJpzvr75lFub4/Z1fQU1FRVtOgOX5h28KahqigjyvvjObaitYmRrwMf+8fHznxu0HH39jNTSxUFY35v3z4fPv7+8fPXz24x8XA6OBpb0yz/vj5+++f/P+3uNXf/985xFXURblfPKJ0UxP0dpUj5vlD2jE6fvHVz9YzbQVrC2N2b++fvTxv5WRupWp4aeHt+98ZTExUtU1NNGQ5WVl52JlYuXj52ViYJSQV1JTUlfV1dDVVJcVE3p89/bHn39+/viuqafHzc4oKq+kqaQuxsfyh4lVWFiEj4P5379/glpmrvqSx0+c+vT6xf07z/7++8HKL62uKPP/58/7N6/9YuJmZ2Vk4+LS0NL58+oZh5iSqoqqsa4OB+t/eTU9TSXpf79/QmKaifE/j5ievY36xWPHHn/8/P8vKFcxs7KzsLDw8AtpaGopKcr++vjy/uPXf/9+5xZR1JCTYOfmEhISEuTn5+bm4uPlfnT3sYCimqqypqGuFg8Hy4enD95///nj5w/sjmdnBk0fgub4GZ4/uMcrq6GsrKYuyv7uy38hUSEeAV5hUfGfrx+//fbj50+E9zWUVMX5Wb/9+QeK9///+SQVddSVlNX1dHQ1FCT5GX78+fef3cLNW+L/g2MXbn1mlXB1NL9x5uiDV5/+MTKxs7Fy8PKqaulpqMj9fv/q1V8uWyNVM0NdIW5wKw5kIiQkhjzJyMT4l4FBRFaF7cPNC89+GxlbsP+4e+PxX2GB3w+evPn/44eIso6iOM9fUBr9I6KobaOrYqAm//LpfU4xZS1F0Wcv36oZmWoYW3H8fvP5v4CplbWirhXX9/sXbz7TMTPXMLb6//mtupW7NOP9E9eevXr/6MnLL/++/wSZKcb79z/jP2ZWpt9fr928y8HHz/zvL6+wgoaWoryEwJ9v4CG7n69OHj97/eqVm9cvnLnyhpWTnZWZ4fGtc0tXb7714LWitomJuryOuszNM0d/CSrpqiirKwsysgmLML+89JLTRFng4pUHMqpCL56+/fPzp6iyjpwoJyuvtLay6LOnj378+veDhd9QXebFk9eqBiYG9hZSPOy/QTO6DE9fvlLWNNMytVGRYLt+/5GajrWKtrko+8eL126raZkpa+gY6GuwMrNyszLy8PKzMjPwCsmpaymrKOpoG2qrKEh8evvoyavPf3/8EFXWlRXh5OKX1dRWkpPg//ebkV9IWICfF7TPhVvO293qxqkjzz98e3b//uc/P3/9/KOtqyXIz/L51YO7T7/zC3P++8Moq6wmJsjz+DODkYGKnoGJqgQnn4iUsrKuAO//338YGBn+yRraWSvzHD968u03FlMztSvHT7IIymhrmDrYa18+duzxh8/s7FwSkvzPHt//9f3Prz9c+lqKLExsCspqyqpq2hp6GtrqAuz/f4PyLgMjM/Pfr5/uP375598PHiE5DQ1VAw3p0/v3/uSR5f/3/c3Xz79//tbR1eJkYZSUVZQU5GNi/Hnv+q1/PMIMP95/ZhS00FY0NtDhZ//PwsbJwsgkoaCkqawuI8z54OEzGT0jJVV9HRXJv+DVM0M+w1AVgCpgWhcmEPNhNShouRREhBiPMDH8/f+fzcBQ9tTOnc+fPTu2c8WaXRf5BPlfPLp968rNF58////z69PnLwwMf75+fPfj/38ZGWkhcTl9DVUeft4/3z9+/vL967cvP37//ffr27MXryGzgZ++/Q2KS7i3a+m5t1xqkkIyujrqMiLsnIIqksyHz9yTlxXgE5d7cn7ff1l9hg/Xn3/kM9CTf37j6p27Vx99/CvOy/721ev/DEwyvIxnrz44dOzU62+MChpG5sY6AgyfL547de/qqZ98km+unrj3W9RISeLDh3cv3rxT0rdX4Xh/9u5jVVUlAXFVLRUZARkJeZ5/Z67cP3Ts9NuvoBzAxCkgw8tw5uqDg4ePf2QRkOdnPHbu5rHT5/nkVVW4/5w5d+/yhdM3n/749e3VnXs3Hj5+/5/h7+ePn378+P7l0+efv39/+PBRSEZBiF9QW1dfWlyMnf3/pw8ff///8/nzt///GT68ffnu229GRoZvb14ziavbGUicPHddQkFZWExWT01NRITt+JFjHMq6ckKMbz9+/fXj+/v3nwRk5L49v3Pv3sMzF85++Prz+7dvX798+fEL1FYATUH9/vnpw/PfrBIe9kYXjhx59wsUpX9+fgdVgF+/fPn2++unD3/ZuBRkpIQkVLU1FHl52H5//vztx69vX778ArtWRkn2w/1bd+/fPXvx4vvP3/kl5IT5BbV1sDn+3at3X3/8+PHjPyPjP4b/kgpKn5/cuHv31o1XP8RFuD68+/D/H8PbF0+ZBCRFBYV1kEz48//vly8g74NS2v9/Xz5/+Pbjx+cvn378/P3ly9ff//79/P755csPaiYuUowvzl+8/JdN0M3Z6ubpk88///z98+cPkI+/ffv08Tcbjxj7r0Onbpy5fOv7H1CWAa3iABlKDwwKWbA9kBwE54LFoAREEEKCynfQajGoFBEU45+//zkFZb69u3v3wScxYaF/r5/dePFWRUZNRJRHQk1PQ0Gah4MdNCXLwvb1+Z2T1+9fuP1MRl7xx/tXH379l5cSuXn21M1zx3+wCQuy/bh69tT9Kyd+86kbakpeOXXyxvnjv1n5fn79qGLsJvHnwfV7PxSVxMRUdMBmgqfsvzzee/CMhoEp569PX378/P7147ff/79++fITPN9w8chBHh1HF1NDf3/Xh+cPPv34++ePn6LSytaWZtKiAq/u37hw58HVm690LG1Z39+7fPfu5cuPxdXVBT4/O/v0m6kK16ErjzXltURFeSRVddUVpHg5Ob58fvfx5385WRlBYXE9A1VBbjFFebE7F89f3H/k7ptPrOC1SQoyMvdunLl26sCV+580FOVuXTl65+rJ1z/5DbRUb109fe/OlQsXbjIz/Lx+896dBw9//mf4/vnjt28/vnz+8uPn708f37NzS0pLC4nDbPz86f33P/+/fvn85z/Dj8/vXr/5wMDA8O/Xx09/eT1cba6cOPZfWFaUX1RbR09BXuLV1dNnH/8zM1L69Pbdzz+/P71795+BSY7n/7kL969ePn/p7stfP378+fvty5fvoCKDgfHTq9fcinqW6rwHjl4UV9V/enb/L1FN1l9vvjBJutsbXjh56t3vv6+fvBGVkuMTE9LV05EQ5GP4+/Pj5y8/vn37/OXbz19fv36DNqn//Pj2m4UdlGHFVXW0FFlZWVU1Nc/vWvOSRVpLXYabW1RbV19Kip+d4e/7j18ZGP7u37NfXMNAjPXLNwZOfqbPRy/duXDtzi8Gtl/fv/z8/evzp0+//v1+9/6ThKzki6vnb924cPf5RzbQIAPY4UQkStoBSD4CZxOQJfBcA+JQG0MMh5BYzQYNfmKVoLog7CQf0souRkamP//+qxh7/GE8dvzwEVZRBS9HIz6WXwZPdl5/zuJoZyzG899EmPXff1Yba2tGJhZ7X8/dWw7uvc9r6W6npm/1X4SV77emngi/IrfAo6tvf/z9z8HMoKpjxC6tpSX69+l/ATd3s107t/JLqdmpMLJJy+uo/xNgY/j3j9vVL0RcTeofh9s3HmE+KQV3419nzlyz9PZR4/7w7Y/o3/9MHv4+e/ZfYpPSt9CW5RUU4WVg4GQwNfp18/LNpw4uvgqc737uPXbnLZ+VjRIfO+uxwycYhbX9XSw5Pj/duvPwYXZhaycbd2+vnXvOsUrpW2rL/ANNPXF4+Pns2X+BTVJXW1NFTVR518bdb4SVPBxMmD5L7tx7/B2nuLu9ucOPH1cu3jNzcRQXEjAx52VhYTcyNmFmYtbQ0pLW1RVi+LF7604pLRMZBQUzcx5GBiYjM1MpcbkvKvdfv/osKS/Ewcv3/szud3+5fIPDpbnf7tq4b+8DPnNXW1cPl8PnrrIIaxjISwn84/3PzcImruNu+vnUidPKJiZ65rZMwix8ApomvOzgxTEMksp6/AycP17cOf3sm39EpAQn07///0WVtHV+c7D9EODkZ2ZV0mFiFNcUUdq85cBhDmE7Z1k5DYN//Py/9HR5BQT+6+kp6BuKMR05fvKCrrmllo4Bk4Tmv6fndm/dKa2N7vjPKo/fPn149f4jY3tXIca/vHL6/ja/j565aOjqpyLL/UlLS0hYWllW8DeftIfp+51bd0rDvM/AwGhkaS3EwQhaxsrKY2VpxcbComNkzM/MxKRtwMQnzMVmLczFeuLUMXY5y1Br3aunD1x4+NUtMEhd8Bcj5xdeJnkWYXaB3xrawtxK/PInz9x9/+s/vxBoCBF07T3VcwgOA+ElFvgcaNB4MaZCiBoICZonJy2TMTCBVsYyGVm6qbBI/vv/X9vSif0TGxsHn6e77e5DO9hFFGwtVcCW/mdlZb194YycopG1kfbl/995/zMoOgZ+ObTj/F1Wn0D3F6fWfXnz/ModZgcnNwX+f//2b794l8030Jvzze1th49xyZkG2Jt+e3Jt55GdEDP///vHzCvn4Wp54/IlGUMXWVEuMTMrfiYGBT3L3/y8//7/ZxaUcdE1FhVjFxUXsf97kYWXy0Scl4ObX4Gbn+HXB0bW/9dPX5BWM7Y11Pogwrnn2FlJE3dDCb63+ja+qrziSlz+jpJsXAIe7ta7D+9gE1FytpQ3sbTk+c+o4OD3ZveWnfvu65o6Gzn5/dyz+cFPfkdbTcb////8Y9Cw9vp0ePf11+xWZrraJqq/fu86c/2VmztoDvjXwZ0nL7wzsXX3dGfaf+6iuK6jurywEJM5PxuLmpERHzvzH2WdP4LqxjIsWw/sYBdVcraUM7a04mNkUNK1YJYUl2ZWffT21V8FARY2LsZvz4/e+uro42eoIsH5a9/2HXsUtU2MTJw/fd134fY7JytLbhERS1Ohv/+Z3Px89+w9/oFfxlxDj0X4DyMjq6WVNTfr/3//Gbn5+Z6fPviNRSQ0xIGNlVFOS19Zmo+R5fffj6eO3fvjFRCkJvHn+fNzwhr22l92b9tzWNfMSU5Zx/A7Nysfs7GuMBMTv6GxHhso9hlEFDR1/gpoS+ps2bz/MKeIuZ0MC59gUFQUv7Iwv6SMjebnHTv2KOqZy8kqWxqL/vvP5OXlefrmZR5pAwUFRRku4TNXHn3/yyrIw6OkbfCLi0uK9xcjA7O+sbGCkibTtx8P7z3iEhXiBC1GBe3sAKelASOQayLQOlvaOwSeMTGtgq6ChlTREHU0Yv8HrUEDLWH/9xe0ClqYhwu8CQjTSVhE/oO2IUHcBV6Ii7SGELLEEbLcEb60GNkIyKpICAlSAFpXCQp28MJ4UEEFNffv5z0bd/IYOFgoifz6858NtFYYYjbD77+gZdgQM/8wgCZs//wDLbEGj0WChH//Ba0OAq1UBfFAGKIMxAK3qSFWgFYFMyEtpgUbBVEDWWXKCFs8Cepigtd4g4b9/oG2F0AaShBfQLRAHAfxOGQhJVghxCrwHgzwtgoWJkbQfgywMMgWZAfArAAX2aCRCYjJEBLZ1xARiO0gQ6BhCBEGkRBBZsQGnf+Q4EUOB5A6MIa4HNm1YGEEAXEw1FP/GUDzRv9+PXjyQkJGjhW0uwo0+wVRDV7CCto9AlmRCxEE9TDAuiAmgNYwg6xkZGEE7SqB7a8AhwhcAwNobQiofACLIK8UhXjh5K7Nj//8Z/****************************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**********************************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", "text/plain": ["<PIL.Image.Image image mode=RGB size=640x360>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Generate agenda slide\n", "agenda_slide_content = presentation_plan['processed_slide_content']['slide_content']['slide_2']\n", "agenda_slide_html = generator.generate_agenda_slide(query=query, slide_content=agenda_slide_content, title_slide_html=title_slide_html, generator_llm=llm2, reviewer_llm=llm2)\n", "\n", "# Display the agenda slide\n", "agenda_slide_img = renderer.renderHTML(html_str=agenda_slide_html, resize=True, resize_ratio=0.5)\n", "display(agenda_slide_img)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2025-07-13 03:52:30.993\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.generation\u001b[0m:\u001b[36mgenerate_general_slide\u001b[0m:\u001b[36m187\u001b[0m - \u001b[1mGenerating slide...\u001b[0m\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "\u001b[32m2025-07-13 03:52:57.971\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mpptx_generation.generation\u001b[0m:\u001b[36mgenerate_general_slide\u001b[0m:\u001b[36m195\u001b[0m - \u001b[1mReviewing generated HTML...\u001b[0m\n", "\u001b[32m2025-07-13 03:53:00.277\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1mSaving rendered HTML image to temp file: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n", "\u001b[32m2025-07-13 03:53:00.294\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mTemp file removed: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "\u001b[32m2025-07-13 03:53:35.277\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m12\u001b[0m - \u001b[1mSaving rendered HTML image to temp file: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n", "\u001b[32m2025-07-13 03:53:35.308\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36mhtmlrender.renderer\u001b[0m:\u001b[36mrenderHTML\u001b[0m:\u001b[36m26\u001b[0m - \u001b[1mTemp file removed: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\html_render.png\u001b[0m\n"]}, {"data": {"image/jpeg": "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", "image/png": "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******************************************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", "text/plain": ["<PIL.Image.Image image mode=RGB size=640x360>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Generate one more slide for testing\n", "existing_slide_content = [\n", "    {'name': 'Title slide', 'html': title_slide_html},\n", "    {'name': 'Agenda slide', 'html': agenda_slide_html}\n", "]\n", "\n", "slide_3_content = presentation_plan['processed_slide_content']['slide_content']['slide_3']\n", "slide_3_html = generator.generate_general_slide(query=query, slide_content=slide_3_content, existing_slide_content=existing_slide_content, generator_llm=llm2, reviewer_llm=llm2)\n", "\n", "# Display the third slide\n", "slide_3_img = renderer.renderHTML(html_str=slide_3_html, resize=True, resize_ratio=0.5)\n", "display(slide_3_img)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Collect all slides for translation\n", "all_slides = [\n", "    {'name': 'Title slide', 'html': title_slide_html},\n", "    {'name': 'Agenda slide', 'html': agenda_slide_html},\n", "    {'name': 'Slide 3', 'html': slide_3_html}\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: LLM-Based Translation"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def llm_html_to_pptxgenjs(html_content, slide_name, llm):\n", "    \"\"\"\n", "    Use LLM to translate HTML to PptxGenJS code with improved prompt and validation\n", "    \"\"\"\n", "    prompt = textwrap.dedent(f\"\"\"\n", "    You are an expert JavaScript developer specializing in PptxGenJS. Convert this HTML slide to PptxGenJS code.\n", "    \n", "    STEP 1: ANALYZE THE HTML FIRST\n", "    Before generating code, carefully examine the HTML for:\n", "    - Background colors (from CSS styles, body, containers)\n", "    - Text colors (from CSS classes, inline styles, color properties)\n", "    - Font sizes and hierarchy (h1, h2, h3, p tags and their CSS)\n", "    - Layout structure (grid, flexbox, columns, sections)\n", "    - Content emphasis (bold, italic, color variations)\n", "    \n", "    STEP 2: EXTRACT DYNAMIC VALUES\n", "    - Background: Look for background-color, background, or theme colors\n", "    - Text colors: Extract from color properties, CSS classes\n", "    - Font sizes: Map HTML hierarchy → h1=32-40px, h2=20-28px, h3=16-20px, p=12-16px\n", "    - Layout: Detect grid patterns, column structures, content sections\n", "    \n", "    CSS-TO-PPTXGENJS MAPPING GUIDE:\n", "    - background-color: #0D1B2A → slide.background = {{ color: '0D1B2A' }}\n", "    - color: #E0E0E0 → color: 'E0E0E0'\n", "    - font-weight: bold → bold: true\n", "    - font-style: italic → italic: true\n", "    - text-align: center → align: 'center'\n", "    - Remove # from hex colors: #FF6B6B → 'FF6B6B'\n", "    \n", "    FALLBACK VALUES (use if HTML analysis unclear):\n", "    - Default background: '0D1B2A' (dark blue)\n", "    - Default text color: 'E0E0E0' (light gray)\n", "    - Default accent color: '88CCEE' (light blue)\n", "    - Default title size: 32, subtitle: 20, content: 14\n", "    \n", "    STEP 3: GENERATE PPTXGENJS CODE\n", "    CRITICAL RULES - <PERSON><PERSON><PERSON><PERSON> EXACTLY:\n", "    1. Generate a COMPLETE function called createPresentation()\n", "    2. ALWAYS start with: const pptx = new PptxGenJS();\n", "    3. ALWAYS end with: return pptx.writeFile({{ fileName: 'presentation.pptx' }});\n", "    4. Use ONLY simple addText() calls - NO complex nested objects\n", "    5. Colors MUST be literal strings extracted from HTML: color: 'E0E0E0' (NOT variables)\n", "    \n", "    POSITIONING RULES - CRITICAL FOR PROPER LAYOUT:\n", "    6. SLIDE DIMENSIONS: Standard slide is 10 inches wide × 5.625 inches tall\n", "    7. SAFE MARGINS: Keep content between x: 0.5-9.5 and y: 0.5-5.0\n", "    8. TITLE POSITIONING: Main titles at y: 0.5-1.0, subtitles at y: 1.2-1.8\n", "    9. CONTENT SPACING: Leave 0.3-0.5 inches between text blocks vertically\n", "    10. GRID LAYOUTS: For 2-column layouts use x: 0.5,5.25 with w: 4.0 each\n", "    11. TEXT WIDTH: Always specify w: (width) to prevent text overflow\n", "    12. VERTICAL FLOW: Start content at y: 2.0, increment by 0.8-1.0 per section\n", "    \n", "    LAYOUT GUIDELINES:\n", "    13. For backgrounds: slide.background = {{ color: '0D1B2A' }};\n", "    14. For bullet points: Use simple text with bullet symbols or newlines\n", "    15. NO overlapping elements - check y positions don't conflict\n", "    16. Keep it SIMPLE - one addText() call per text element\n", "    \n", "    HTML to convert:\n", "    {html_content}\n", "    \n", "    Follow these SIMPLE examples EXACTLY:\n", "    \n", "    EXAMPLE 1 - Dynamic color extraction from HTML:\n", "    \n", "    HTML Input: <body style='background-color: #2C3E50'><h1 style='color: #ECF0F1'>Title</h1></body>\n", "    \n", "    ```javascript\n", "    function createPresentation() {{\n", "        const pptx = new PptxGenJS();\n", "        const slide = pptx.addSlide();\n", "        \n", "        // EXTRACTED: background-color: #2C3E50 → '2C3E50'\n", "        slide.background = {{ color: '2C3E50' }};\n", "        \n", "        // EXTRACTED: h1 + color: #ECF0F1 → fontSize: 36, color: 'ECF0F1'\n", "        slide.addText('AI-Powered Document Search', {{\n", "            x: 0.5,\n", "            y: 0.8,\n", "            w: 9.0,\n", "            h: 1.2,\n", "            fontSize: 36,\n", "            color: 'ECF0F1',\n", "            bold: true,\n", "            align: 'center'\n", "        }});\n", "        \n", "        // Subtitle - proper spacing below title\n", "        slide.addText('A Proposal for Intelligent Document Discovery', {{\n", "            x: 0.5,\n", "            y: 2.2,\n", "            w: 9.0,\n", "            h: 0.8,\n", "            fontSize: 18,\n", "            color: '88CCEE',\n", "            align: 'center'\n", "        }});\n", "        \n", "        // Author - bottom section\n", "        slide.addText('<PERSON>, Tech Consultant', {{\n", "            x: 0.5,\n", "            y: 4.0,\n", "            w: 9.0,\n", "            h: 0.5,\n", "            fontSize: 16,\n", "            color: 'E0E0E0',\n", "            align: 'center'\n", "        }});\n", "        \n", "        return pptx.writeFile({{ fileName: 'presentation.pptx' }});\n", "    }}\n", "    ```\n", "    \n", "    EXAMPLE 2 - Semantic HTML analysis for grid layout:\n", "    \n", "    HTML Input: <div class='grid-2x2' style='background: #1A252F'>\n", "                  <h1 style='color: #FFFFFF'>Executive Summary</h1>\n", "                  <section class='problem'><h3 style='color: #E74C3C'>Problem</h3></section>\n", "                  <section class='solution'><h3 style='color: #2ECC71'>Solution</h3></section>\n", "                </div>\n", "    \n", "    ```javascript\n", "    function createPresentation() {{\n", "        const pptx = new PptxGenJS();\n", "        const slide = pptx.addSlide();\n", "        \n", "        // EXTRACTED: background: #1A252F → '1A252F'\n", "        slide.background = {{ color: '1A252F' }};\n", "        \n", "        // EXTRACTED: h1 + color: #FFFFFF → fontSize: 32, color: 'FFFFFF'\n", "        slide.addText('Executive Summary', {{\n", "            x: 0.5,\n", "            y: 0.5,\n", "            w: 9.0,\n", "            h: 0.8,\n", "            fontSize: 32,\n", "            color: 'FFFFFF',\n", "            bold: true\n", "        }});\n", "        \n", "        // DETECTED: .problem section + h3 color: #E74C3C → 'E74C3C'\n", "        slide.addText('❓ Problem', {{\n", "            x: 0.5,\n", "            y: 1.5,\n", "            w: 4.0,\n", "            h: 0.5,\n", "            fontSize: 18,\n", "            color: 'E74C3C',\n", "            bold: true\n", "        }});\n", "        \n", "        // EXTRACTED: p tag content with default text color\n", "        slide.addText('Current keyword-based search is inefficient', {{\n", "            x: 0.5,\n", "            y: 2.1,\n", "            w: 4.0,\n", "            h: 1.0,\n", "            fontSize: 14,\n", "            color: 'FFFFFF'\n", "        }});\n", "        \n", "        // DETECTED: .solution section + h3 color: #2ECC71 → '2ECC71'\n", "        slide.addText('💡 Solution', {{\n", "            x: 5.25,\n", "            y: 1.5,\n", "            w: 4.0,\n", "            h: 0.5,\n", "            fontSize: 18,\n", "            color: '2ECC71',\n", "            bold: true\n", "        }});\n", "        \n", "        slide.addText('AI-powered semantic search system', {{\n", "            x: 5.25,\n", "            y: 2.1,\n", "            w: 4.0,\n", "            h: 1.0,\n", "            fontSize: 12,\n", "            color: 'E0E0E0'\n", "        }});\n", "        \n", "        // BOTTOM-LEFT: Benefits section\n", "        slide.addText('✅ Key Benefits', {{\n", "            x: 0.5,\n", "            y: 3.4,\n", "            w: 4.0,\n", "            h: 0.5,\n", "            fontSize: 18,\n", "            color: '95E1D3',\n", "            bold: true\n", "        }});\n", "        \n", "        slide.addText('• Improved search relevance\\n• Faster access to information', {{\n", "            x: 0.5,\n", "            y: 4.0,\n", "            w: 4.0,\n", "            h: 1.0,\n", "            fontSize: 12,\n", "            color: 'E0E0E0'\n", "        }});\n", "        \n", "        // BOTTOM-RIGHT: Call to Action\n", "        slide.addText('🚀 Call to Action', {{\n", "            x: 5.25,\n", "            y: 3.4,\n", "            w: 4.0,\n", "            h: 0.5,\n", "            fontSize: 18,\n", "            color: 'F38BA8',\n", "            bold: true\n", "        }});\n", "        \n", "        slide.addText('Approve this proposal to transform document access', {{\n", "            x: 5.25,\n", "            y: 4.0,\n", "            w: 4.0,\n", "            h: 1.0,\n", "            fontSize: 12,\n", "            color: 'E0E0E0'\n", "        }});\n", "        \n", "        return pptx.writeFile({{ fileName: 'presentation.pptx' }});\n", "    }}\n", "    ```\n", "    \n", "    HTML ANALYSIS PATTERNS:\n", "    - <h1> tags → fontSize: 32-40, bold: true, main title positioning\n", "    - <h2> tags → fontSize: 20-28, bold: true, section header positioning\n", "    - <h3> tags → fontSize: 16-20, bold: true, subsection positioning\n", "    - <p> tags → fontSize: 12-16, regular text\n", "    - CSS classes like .grid, .columns → Use 2-column layout template\n", "    - CSS classes like .hero, .title → Use centered title layout\n", "    - inline style='color: #...' → Extract hex color, remove #\n", "    - CSS background-color → slide.background color\n", "    - font-weight: bold → bold: true\n", "    - text-align: center → align: 'center'\n", "    \n", "    CRITICAL SYNTAX RULES - THESE PREVENT ERRORS:\n", "    1. Colors: ALWAYS extract from HTML and use quoted strings like color: 'E0E0E0'\n", "    2. Remove # from hex colors: #FF6B6B → 'FF6B6B'\n", "    3. Properties: NEVER add extra quotes like bold: true',' (CORRECT: bold: true)\n", "    4. Objects: NEVER break object syntax like {{ fontSize: 18, color: 'accentColor, bold: true }}\n", "    5. NO nested text objects - keep each addText() call simple\n", "    6. NO variable references in strings - use actual extracted color values\n", "    7. For multiple bullet points, use newlines: 'First point\\nSecond point'\n", "    8. ALWAYS close all quotes, brackets, and parentheses properly\n", "    \n", "    POSITIONING VALIDATION - PREVENT OVERLAPS:\n", "    8. CHECK Y-POSITIONS: Ensure no two elements have overlapping y-ranges\n", "    9. TITLE ZONE: y: 0.5-1.3 (reserve for main titles only)\n", "    10. CONTENT ZONES: Start at y: 1.5+ with 0.8+ spacing between sections\n", "    11. GRID COLUMNS: Left column x: 0.5, Right column x: 5.25 (never overlap)\n", "    12. ALWAYS specify both w: and h: to control text box boundaries\n", "    13. Use the EXACT positioning from examples - don't improvise\n", "    \n", "    Generate ONLY the JavaScript function. Keep it simple and syntactically correct.\n", "    \"\"\")\n", "    \n", "    response = llm.call(query=prompt)\n", "    code = response['text']\n", "    \n", "    # Clean up the response to remove any markdown code blocks\n", "    code = code.replace('```javascript', '').replace('```js', '').replace('```', '')\n", "    \n", "    # RULE-BASED CLEANUP: Remove problematic statements\n", "    import re\n", "    \n", "    # Remove import/require statements\n", "    code = re.sub(r'.*\\\\b(import|require)\\\\b.*', '', code)\n", "    \n", "    # DON'T remove pptx declarations - they are needed!\n", "    # DON'T remove writeFile calls - they are needed!\n", "    \n", "    # Remove function wrappers\n", "    code = re.sub(r'.*function\\\\s+\\\\w+.*\\\\{.*', '', code)\n", "    code = re.sub(r'^\\\\s*\\\\}\\\\s*$', '', code, flags=re.MULTILINE)\n", "    \n", "    # Remove export statements\n", "    code = re.sub(r'.*(export|module\\\\.exports).*', '', code)\n", "    \n", "    # Simplify complex gradient backgrounds to solid colors\n", "    gradient_pattern = r'slide\\.background\\s*=\\s*\\{[^}]*fill:\\s*\\{[^}]*gradient[^}]*\\}[^}]*\\}'\n", "    code = re.sub(gradient_pattern, 'slide.background = { color: \\'1B263B\\' }', code, flags=re.DOTALL)\n", "    \n", "    # Basic validation to ensure proper color formatting\n", "    \n", "    # Find patterns like color: #FFFFFF or color: FFFFFF (without quotes)\n", "    color_pattern = r'color:\\s*([^\\'\\\"]+)([,}])'\n", "    code = re.sub(color_pattern, lambda m: f'color: \\'{m.group(1).strip()}\\'{m.group(2)}', code)\n", "    \n", "    # Find patterns like { color: #FFFFFF } or { color: FFFFFF } (without quotes)\n", "    obj_color_pattern = r'{\\s*color:\\s*([^\\'\\\"]+)([,}])'\n", "    code = re.sub(obj_color_pattern, lambda m: f'{{ color: \\'{m.group(1).strip()}\\'{m.group(2)}', code)\n", "    \n", "    # Check for unclosed string literals\n", "    def fix_unclosed_strings(code):\n", "        lines = code.split('\\n')\n", "        for i, line in enumerate(lines):\n", "            # Check for lines with odd number of single quotes (likely unclosed string)\n", "            if line.count(\"'\") % 2 != 0 and \"'\" in line:\n", "                # Add closing quote at the end if it seems to be missing\n", "                if not line.strip().endswith(\"'\"):\n", "                    lines[i] = line + \"'\"\n", "        return '\\n'.join(lines)\n", "    \n", "    code = fix_unclosed_strings(code)\n", "    \n", "    # Replace any variable references in color values with literal strings\n", "    var_in_color_pattern = r'color:\\s*\\'([^\\']*\\$\\{[^}]*\\}[^\\']*)\\'([,}])'\n", "    code = re.sub(var_in_color_pattern, lambda m: f'color: \\'000000\\'{m.group(2)}', code)\n", "    \n", "    var_name_pattern = r'color:\\s*([a-zA-Z][a-zA-Z0-9_]*)([,}])'\n", "    code = re.sub(var_name_pattern, lambda m: f'color: \\'000000\\'{m.group(2)}', code)\n", "    \n", "    # Fix specific syntax errors found in complex slides\n", "    # Fix: color: 'textColorLight,' -> color: 'E0E0E0',\n", "    code = re.sub(r'color:\\s*\\'[^\\',}]*,', 'color: \\'E0E0E0\\',', code)\n", "    \n", "    # Fix: bold: true',' -> bold: true,\n", "    code = re.sub(r'(bold|italic):\\s*true\\'[,}]', r'\\1: true,', code)\n", "    \n", "    # Fix broken object syntax\n", "    code = re.sub(r'color:\\s*\\'[^\\',}]*,\\s*bold:', 'color: \\'88CCEE\\', bold:', code)\n", "    \n", "    # POSITIONING VALIDATION: Fix common positioning issues\n", "    # Fix out-of-bounds x positions (keep within 0.5-9.5)\n", "    def fix_x_position(match):\n", "        value = float(match.group(1))\n", "        fixed_value = min(9.5, max(0.5, value))\n", "        return f'x: {fixed_value}'\n", "    \n", "    code = re.sub(r'x:\\s*([0-9]+\\.?[0-9]*)', fix_x_position, code)\n", "    \n", "    # Fix out-of-bounds y positions (keep within 0.5-5.0)\n", "    def fix_y_position(match):\n", "        value = float(match.group(1))\n", "        fixed_value = min(5.0, max(0.5, value))\n", "        return f'y: {fixed_value}'\n", "    \n", "    code = re.sub(r'y:\\s*([0-9]+\\.?[0-9]*)', fix_y_position, code)\n", "    \n", "    # Final validation: Ensure complete function structure\n", "    code = code.strip()\n", "    \n", "    # Check if it's a complete function\n", "    if not code.startswith('function createPresentation()'):\n", "        # If not, wrap the code in a proper function\n", "        if not 'const pptx = new PptxGenJS()' in code:\n", "            code = 'const pptx = new PptxGenJS();\\n' + code\n", "        if not 'const slide = pptx.addSlide()' in code:\n", "            code = 'const slide = pptx.addSlide();\\n' + code\n", "        if not 'return pptx.writeFile' in code:\n", "            code = code + '\\n\\nreturn pptx.writeFile({ fileName: \\'llm-generated-presentation.pptx\\' });'\n", "        \n", "        # Wrap in function\n", "        code = f'function createPresentation() {{\\n    {code.replace(chr(10), chr(10) + \"    \")}\\n}}'\n", "    \n", "    # Remove empty lines but keep structure\n", "    lines = [line for line in code.split('\\n') if line.strip()]\n", "    code = '\\n'.join(lines)\n", "    \n", "    return code"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n", "INFO:google_genai.models:AFC is enabled with max remote calls: 10.\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent \"HTTP/1.1 200 OK\"\n", "INFO:google_genai.models:AFC remote call 1 is done.\n"]}], "source": ["# Test LLM-based translation for one slide\n", "llm_full_script1 = llm_html_to_pptxgenjs(title_slide_html, \"Title Slide\", llm3)\n", "llm_full_script2 = llm_html_to_pptxgenjs(agenda_slide_html, \"Agenda Slide\", llm3)\n", "llm_full_script3 = llm_html_to_pptxgenjs(slide_3_html, \"Slide 3\", llm3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Generate PPTX File Using Node.js"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Create Node.js scripts for LLM-based approach\n", "llm_based_node_script1 = f\"\"\"\n", "const PptxGenJS = require('pptxgenjs');\n", "{llm_full_script1}\n", "createPresentation()\n", "    .then(() => {{\n", "        console.log('LLM-based PPTX file generated successfully!');\n", "        console.log('File saved as: llm-generated-presentation.pptx');\n", "    }})\n", "    .catch(error => {{\n", "        console.error('Error generating presentation:', error);\n", "    }});\n", "\"\"\"\n", "\n", "with open(\"llm_based_generate1_pptx.js\", \"w\") as f:\n", "    f.write(llm_based_node_script1)\n", "\n", "llm_based_node_script2 = f\"\"\"\n", "const PptxGenJS = require('pptxgenjs');\n", "{llm_full_script2}\n", "createPresentation()\n", "    .then(() => {{\n", "        console.log('LLM-based PPTX file generated successfully!');\n", "        console.log('File saved as: llm-generated-presentation.pptx');\n", "    }})\n", "    .catch(error => {{\n", "        console.error('Error generating presentation:', error);\n", "    }});\n", "\"\"\"\n", "\n", "with open(\"llm_based_generate2_pptx.js\", \"w\") as f:\n", "    f.write(llm_based_node_script2)\n", "    \n", "llm_based_node_script3 = f\"\"\"\n", "// Import PptxGenJS\n", "const PptxGenJS = require('pptxgenjs');\n", "{llm_full_script3}\n", "\n", "createPresentation()\n", "    .then(() => {{\n", "        console.log('LLM-based PPTX file generated successfully!');\n", "        console.log('File saved as: llm-generated-presentation.pptx');\n", "    }})\n", "    .catch(error => {{\n", "        console.error('Error generating presentation:', error);\n", "    }});\n", "\"\"\"\n", "\n", "with open(\"llm_based_generate3_pptx.js\", \"w\") as f:\n", "    f.write(llm_based_node_script3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- LLM-BASED APPROACH ---\n", "LLM-based PPTX file generated successfully!\n", "File saved as: llm-generated-presentation.pptx\n", "\n", "File created: c:\\Users\\<USER>\\source\\pptx-planner\\experimentation\\presentation2.pptx\n", "\n", "--- SUMMARY ---\n", "LLM-based approach: Success\n"]}], "source": ["# Run the Node.js scripts to generate the PPTX files\n", "import subprocess\n", "import os\n", "\n", "def run_node_script(script_path, output_file):\n", "    try:\n", "        # Run the Node.js script\n", "        result = subprocess.run([\"node\", script_path], \n", "                               check=True, \n", "                               capture_output=True, \n", "                               text=True)\n", "        \n", "        print(result.stdout)\n", "        \n", "        # Check if the file was created\n", "        if os.path.exists(output_file):\n", "            print(f\"File created: {os.path.abspath(output_file)}\")\n", "            return True\n", "        else:\n", "            print(f\"Warning: {output_file} not found in the expected location.\")\n", "            return False\n", "            \n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"Error running Node.js script: {e}\")\n", "        print(f\"Error output: {e.stderr}\")\n", "        print(\"\\nMake sure you have Node.js installed and have run 'npm install pptxgenjs'\")\n", "        return False\n", "\n", "try:\n", "    # Run LLM-based approach\n", "    print(\"\\n--- LLM-BASED APPROACH ---\")\n", "    llm_based_success = run_node_script(\"llm_based_generate2_pptx.js\", \"presentation1.pptx\")\n", "    \n", "    # Summary\n", "    print(\"\\n--- SUMMARY ---\")\n", "    print(f\"LLM-based approach: {'Success' if llm_based_success else 'Failed'}\")\n", "    \n", "except FileNotFoundError:\n", "    print(\"Error: Node.js not found. Please install Node.js from https://nodejs.org/\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 4}