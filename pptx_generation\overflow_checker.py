import os
from playwright.sync_api import sync_playwright
from playwright.async_api import async_playwright

# --- MODIFIED JavaScript for overflow detection and optional auto-adjustment ---
# The JavaScript function now accepts a single 'params' object
js_function_string = """
(function(params) { // Expecting a single 'params' object from Python
    const slideContent = document.querySelector('.slide-content');
    if (!slideContent) {
        return { status: 'error', message: '.slide-content element not found.' };
    }

    // Destructure parameters for easier use
    const enableAutoAdjust = params.enableAutoAdjust;
    const minFontSize = params.minFontSize;
    const reductionFactor = params.reductionFactor;

    // --- CORRECTED AND CONSOLIDATED elementsToAdjust ---
    const elementsToAdjust = [
        // Top-level text elements. Using slideContent.querySelector for better scope.
        //slideContent.querySelector('.text-slide-title'),
        slideContent.querySelector('.text-slide-subtitle'),
        slideContent.querySelector('.text-slide-body'),
        slideContent.querySelector('.text-detail-body'),
        // If you have a specific element like .venn-overlap-text that holds direct text, add it here:
        // slideContent.querySelector('.venn-overlap-text'), 

        // Target specific headings, paragraphs, and list items within content boxes OR section elements.
        // Removed direct targeting of `ul` as it's a container, not a direct text holder.
        ...slideContent.querySelectorAll(
            '.content-box .text-section-heading, .content-box h3, .content-box p, .content-box li, ' +
            '.section .text-section-heading, .section h3, .section p, .section li'
        )
    ].filter(Boolean); // Filter out any nulls if elements aren't found

    const checkForOverflow = () => {
        const verticalOverflow = slideContent.scrollHeight > slideContent.clientHeight;
        const horizontalOverflow = slideContent.scrollWidth > slideContent.clientWidth;
        return verticalOverflow || horizontalOverflow;
    };

    let hasOverflow = checkForOverflow();
    let iterations = 0;
    const maxIterations = 30;

    if (!hasOverflow) {
        return { status: 'no_overflow', message: 'No overflow detected.' };
    }

    let initialDimensions = {
        scrollHeight: slideContent.scrollHeight,
        clientHeight: slideContent.clientHeight,
        scrollWidth: slideContent.scrollWidth,
        clientWidth: slideContent.clientWidth
    };

    if (enableAutoAdjust && hasOverflow) {
        while (hasOverflow && iterations < maxIterations) {
            let madeChange = false;
            elementsToAdjust.forEach(el => {
                const currentSize = parseFloat(window.getComputedStyle(el).fontSize);
                // Adjusted condition to ensure it can reach minFontSize precisely, or stops just above if reduction takes it too far
                if (currentSize > minFontSize / reductionFactor) { 
                    el.style.fontSize = `${currentSize * reductionFactor}px`;
                    madeChange = true;
                } else if (currentSize > minFontSize) { // If reduction would go below min, set it to min
                    el.style.fontSize = `${minFontSize}px`;
                    madeChange = true;
                }
            });

            if (!madeChange && hasOverflow) {
                break;
            }
            hasOverflow = checkForOverflow();
            iterations++;
        }

        if (hasOverflow) {
            return {
                status: 'critical_overflow',
                message: `Content still overflows after ${iterations} adjustments. Manual intervention needed.`,
                overflow_details: {
                    initial: initialDimensions,
                    final: {
                        scrollHeight: slideContent.scrollHeight,
                        clientHeight: slideContent.clientHeight,
                        scrollWidth: slideContent.scrollWidth,
                        clientWidth: slideContent.clientWidth
                    }
                }
            };
        } else {
            return {
                status: 'resolved_overflow',
                message: `Overflow resolved by reducing font sizes in ${iterations} iterations.`,
                overflow_details: {
                    initial: initialDimensions,
                    final: {
                        scrollHeight: slideContent.scrollHeight,
                        clientHeight: slideContent.clientHeight,
                        scrollWidth: slideContent.scrollWidth,
                        clientWidth: slideContent.clientWidth
                    }
                }
            };
        }
    } else {
        return {
            status: 'initial_overflow_detected',
            message: 'Overflow detected. Auto-adjustment is disabled or failed.',
            overflow_details: {
                initial: initialDimensions,
                final: {
                    scrollHeight: slideContent.scrollHeight,
                    clientHeight: slideContent.clientHeight,
                    scrollWidth: slideContent.scrollWidth,
                    clientWidth: slideContent.clientWidth
                }
            }
        };
    }
})
"""

def check_slide_overflow(html_content, slide_width=1280, slide_height=720, enable_auto_adjust=True):
    """
    Checks for content overflow within a slide using a headless browser.
    Optionally attempts to auto-adjust font sizes if overflow is detected.

    Args:
        html_content (str): The full HTML string of the slide.
        slide_width (int): The target width of the slide in pixels.
        slide_height (int): The target height of the slide in pixels.
        enable_auto_adjust (bool): Whether to attempt font size reduction to fit content.

    Returns:
        dict: A dictionary containing:
            - 'status': 'no_overflow', 'resolved_overflow', 'critical_overflow', 'initial_overflow_detected', 'error'
            - 'message': A descriptive message.
            - 'overflow_details': Dictionary with scroll/client dimensions if applicable.
            - 'screenshot_path': Path to a generated screenshot for debugging (if overflow detected).
    """
    screenshot_path = None
    result = {
        'status': 'error',
        'message': 'An unexpected error occurred.',
        'overflow_details': {},
        'screenshot_path': None,
        'modified_html': None
    }

    with sync_playwright() as p:
        browser = None
        try:
            browser = p.chromium.launch()
            page = browser.new_page()
            page.set_viewport_size({"width": slide_width, "height": slide_height})
            page.set_content(html_content)
            page.wait_for_load_state('networkidle')

            # --- MODIFIED: Package arguments into a dictionary ---
            js_args = {
                'enableAutoAdjust': enable_auto_adjust,
                'minFontSize': 12, # Directly use numerical values here
                'reductionFactor': 0.98
            }
            # Pass the JavaScript function string and the single 'js_args' dictionary
            js_result = page.evaluate(js_function_string, js_args)
            result.update(js_result)

            # --- Retrieve modified HTML if auto-adjustment occurred ---
            if enable_auto_adjust and result['status'] in ['resolved_overflow', 'critical_overflow']:
                result['modified_html'] = page.content() # Get the HTML content *after* JS modifications

            # if result['status'] in ['initial_overflow_detected', 'critical_overflow', 'resolved_overflow']:
            #     screenshot_filename = f"slide_screenshot_{result['status']}.png"
            #     screenshot_path = os.path.join(os.getcwd(), screenshot_filename)
            #     page.screenshot(path=screenshot_path)
            #     result['screenshot_path'] = screenshot_path

        except Exception as e:
            result['status'] = 'error'
            result['message'] = f"Error during headless browser operation: {e}"
            print(f"Error: {e}")
        finally:
            if browser:
                browser.close()
    return result

async def check_slide_overflow_async(html_content, slide_width=1280, slide_height=720, enable_auto_adjust=True):
    """
    Checks for content overflow within a slide using a headless browser.
    Optionally attempts to auto-adjust font sizes if overflow is detected.

    Args:
        html_content (str): The full HTML string of the slide.
        slide_width (int): The target width of the slide in pixels.
        slide_height (int): The target height of the slide in pixels.
        enable_auto_adjust (bool): Whether to attempt font size reduction to fit content.

    Returns:
        dict: A dictionary containing:
            - 'status': 'no_overflow', 'resolved_overflow', 'critical_overflow', 'initial_overflow_detected', 'error'
            - 'message': A descriptive message.
            - 'overflow_details': Dictionary with scroll/client dimensions if applicable.
            - 'screenshot_path': Path to a generated screenshot for debugging (if overflow detected).
    """
    screenshot_path = None
    result = {
        'status': 'error',
        'message': 'An unexpected error occurred.',
        'overflow_details': {},
        'screenshot_path': None,
        'modified_html': None
    }

    async with async_playwright() as p:
        browser = None
        try:
            browser = await p.chromium.launch()
            page = await browser.new_page()
            await page.set_viewport_size({"width": slide_width, "height": slide_height})
            await page.set_content(html_content)
            await page.wait_for_load_state('networkidle')

            js_args = {
                'enableAutoAdjust': enable_auto_adjust,
                'minFontSize': 12, # Directly use numerical values here
                'reductionFactor': 0.98
            }
            # Pass the JavaScript function string and the single 'js_args' dictionary
            js_result = await page.evaluate(js_function_string, js_args)
            result.update(js_result)

            # --- Retrieve modified HTML if auto-adjustment occurred ---
            if enable_auto_adjust and result['status'] in ['resolved_overflow', 'critical_overflow']:
                result['modified_html'] = await page.content() # Get the HTML content *after* JS modifications

            # if result['status'] in ['initial_overflow_detected', 'critical_overflow', 'resolved_overflow']:
            #     screenshot_filename = f"slide_screenshot_{result['status']}.png"
            #     screenshot_path = os.path.join(os.getcwd(), screenshot_filename)
            #     page.screenshot(path=screenshot_path)
            #     result['screenshot_path'] = screenshot_path

        except Exception as e:
            result['status'] = 'error'
            result['message'] = f"Error during headless browser operation: {e}"
            print(f"Error: {e}")
        finally:
            if browser:
                await browser.close()
    return result