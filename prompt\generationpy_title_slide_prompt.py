import textwrap


#######Daniel's Prompt#######
generationpy_title_slide_prompt_daniel_v1 = textwrap.dedent(
        """
        You are a tech consultant, and you have been given the following request:

        "{query}"

        You are trying to create a set of slides for a proposal.
        The first slide you want to create is the title slide.
        Generate the title slide in HTML.

        Take into consideration the following points:
        - Choose a style that is both visually appealing and functional; befitting of a proposal from a top-tier tech consulting company.
        - What colour and design would be appropriate, especially for the background?
        - What font type should you use?
        - What should the size of the page be, to accurately reflect powerpoint slides? The slides must be 720p
        - The title font should be around 3.0em, and the subtitle around 1.8em, otherwise it is too big.
        - Make sure to include an empty footer e.g. 
            <div class="footer-bar w-full mt-auto relative">
                <div class="absolute bottom-2 right-4 text-white text-sm"></div>
            </div>
            
        This slide will become a template master slide which will define the style of the following slides, so design this slide with great care.
        Do not output any other text other than the html itself.
        If your slides are visually appealing but also functional, you will be rewarded with a bonus.

        The information that should be included on this slide is as follows:
        {slide_content}
        """)


#######Duy's Prompt#######
generationpy_title_slide_prompt_duy_v1 = textwrap.dedent(
        """
        You’re a professional creating a slide deck for:

        "{query}"

        A Professional Designer give you the following css for this Title Slide creation task as below::

        "{brain_storm}"

        Start by generating the Title Slide in full HTML.

        🔧 Requirements:

        Include an empty footer exactly like this:

        html
        Copy
        Edit
        <div class="footer-bar w-full mt-auto relative">
        <div class="absolute bottom-2 right-4 text-white text-sm"></div>
        </div>
        
        This is your master template slide, so design it with great care.

        Output only the HTML code — no explanations or comments.

        Do not truncate code with /* ... */.

        📄 Slide content to include:
        {slide_content}

        💡 Reference examples (do not copy):
        Example 1: {Example_1}
        Example 2: {Example_2}
        """)
generationpy_title_slide_prompt_duy_v2 = textwrap.dedent(
        """
        You’re a professional creating a slide deck for:

        "{query}"

        A Professional Designer give you the following design for this Title Slide creation task as below::

        "{brain_storm}"

        Start by generating the Title Slide in full HTML and Tailwind CSS.

        🔧 Requirements:
        
        This is your master template slide, so design it with great care.
        
        You are provided a tool name "image_tavily" for images/logos/illustrations retrieval.

        Output only the HTML code — no explanations or comments.

        The designer had done his job
        now you only need to pay EXTREME ATTENTION to the position of the element inside the HTML. And CHOOSE THE RIGHT SIZE OF THE ELEMENTS

        # ALERT: AGAIN BE EXTREMELY CAREFUL ABOUT THE ELEMENT POSITIONS And CHOOSE THE RIGHT SIZE OF THE ELEMENTS

        📄 Slide content to include:
        {slide_content}

        Constraints:
        - Minimize the use of animations and transitions.
        - The slides MUST be 720p, and ensure all slide content fits within a height of 720px.
        - Output only the final HTML for the new slide.

        Beyond all your mission is to make this BEAUTIFUL
        """)
#######Hai's Prompt#######
