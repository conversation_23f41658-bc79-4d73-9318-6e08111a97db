####################### Inforgraphic Example #######################################

Example_1_inforgraphic = """<!DOCTYPE html>
        <html lang="en">
        <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Line Graph Example</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
                body {
                margin: 0;
                padding: 0;
                overflow: hidden;
                }
                .slide {
                width: 1280px;
                min-height: 720px;
                background-color: #0F172A;
                color: white;
                position: relative;
                overflow: hidden;
                }
                .circle-decoration {
                position: absolute;
                border-radius: 50%;
                opacity: 0.08;
                }
                .chart-container {
                position: relative;
                height: 400px;
                width: 100%;
                }
        </style>
        </head>
        <body>
        <div class="slide p-10">
                <!-- Background decorative elements -->
                <div class="circle-decoration bg-blue-500" style="width: 400px; height: 400px; top: -200px; right: -100px;"></div>
                <div class="circle-decoration bg-purple-500" style="width: 300px; height: 300px; bottom: -100px; left: -100px;"></div>
                
                <!-- Header -->
                <div class="mb-8">
                <div class="flex justify-between items-center">
                        <h1 class="text-4xl font-bold text-white">Line Graph Example</h1>
                        <div class="text-sm text-gray-400">
                        <i class="fas fa-chart-line mr-2"></i>Tracking Trends Over Time
                        </div>
                </div>
                <div class="h-1 w-20 bg-blue-400 mt-2"></div>
                </div>
                
                <!-- Main Content with Chart -->
                <div class="flex mt-6">
                <!-- Left side - Chart -->
                <div class="w-8/12 pr-6">
                        <div class="bg-gray-800 bg-opacity-50 rounded-lg p-6 border border-gray-700">
                        <div class="chart-container">
                                <canvas id="lineChart"></canvas>
                        </div>
                        </div>
                </div>
                
                <!-- Right side - Key insights -->
                <div class="w-4/12">
                        <div class="bg-gray-800 bg-opacity-50 rounded-lg p-6 border border-gray-700 h-full">
                        <h3 class="text-xl font-bold mb-4 text-blue-400">Key Insights</h3>
                        
                        <div class="space-y-6">
                                <div class="flex items-start">
                                <div class="text-purple-400 mr-3 mt-1">
                                        <i class="fas fa-arrow-trend-up"></i>
                                </div>
                                <div>
                                        <h4 class="font-semibold text-white">Growth Patterns</h4>
                                        <p class="text-gray-300 text-sm">Product B showed consistent growth throughout Q3, peaking at 82% in November.</p>
                                </div>
                                </div>
                                
                                <div class="flex items-start">
                                <div class="text-yellow-400 mr-3 mt-1">
                                        <i class="fas fa-bolt"></i>
                                </div>
                                <div>
                                        <h4 class="font-semibold text-white">Seasonal Impact</h4>
                                        <p class="text-gray-300 text-sm">Product A experienced a significant dip in September before recovering to outperform in December.</p>
                                </div>
                                </div>
                                
                                <div class="flex items-start">
                                <div class="text-teal-400 mr-3 mt-1">
                                        <i class="fas fa-arrows-trend-down"></i>
                                </div>
                                <div>
                                        <h4 class="font-semibold text-white">Market Volatility</h4>
                                        <p class="text-gray-300 text-sm">Both products show increasing volatility in Q4, suggesting potential market fluctuations ahead.</p>
                                </div>
                                </div>
                        </div>
                        </div>
                </div>
                </div>
                
                <!-- Footer with footnote -->
                <div class="absolute bottom-5 left-10 text-gray-500 text-xs">
                <p>*Data represents Q3-Q4 2024 performance metrics</p>
                </div>
        </div>

        <script>
                // Sample data for the line chart
                const ctx = document.getElementById('lineChart').getContext('2d');
                const lineChart = new Chart(ctx, {
                type: 'line',
                data: {
                        labels: ['July', 'August', 'September', 'October', 'November', 'December'],
                        datasets: [
                        {
                                label: 'Product A',
                                data: [65, 59, 42, 56, 68, 85],
                                borderColor: 'rgba(99, 102, 241, 1)',
                                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                                tension: 0.4,
                                fill: true,
                                pointBackgroundColor: 'rgba(99, 102, 241, 1)',
                                pointBorderColor: '#fff',
                                pointRadius: 5,
                                pointHoverRadius: 8
                        },
                        {
                                label: 'Product B',
                                data: [45, 55, 63, 70, 82, 76],
                                borderColor: 'rgba(236, 72, 153, 1)',
                                backgroundColor: 'rgba(236, 72, 153, 0.1)',
                                tension: 0.4,
                                fill: true,
                                pointBackgroundColor: 'rgba(236, 72, 153, 1)',
                                pointBorderColor: '#fff',
                                pointRadius: 5,
                                pointHoverRadius: 8
                        }
                        ]
                },
                options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                        legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                color: 'rgb(229, 231, 235)',
                                padding: 20,
                                font: {
                                        size: 14
                                }
                                }
                        },
                        title: {
                                display: true,
                                text: 'Performance Trends Q3-Q4 2024',
                                color: 'rgb(229, 231, 235)',
                                font: {
                                size: 16
                                },
                                padding: {
                                bottom: 20
                                }
                        },
                        tooltip: {
                                mode: 'index',
                                intersect: false,
                                backgroundColor: 'rgba(15, 23, 42, 0.8)',
                                titleColor: '#fff',
                                bodyColor: '#fff',
                                borderColor: 'rgba(99, 102, 241, 0.5)',
                                borderWidth: 1,
                                padding: 12
                        }
                        },
                        scales: {
                        y: {
                                beginAtZero: true,
                                max: 100,
                                grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                color: 'rgb(229, 231, 235)',
                                callback: function(value) {
                                        return value + '%';
                                }
                                }
                        },
                        x: {
                                grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                color: 'rgb(229, 231, 235)'
                                }
                        }
                        },
                        interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                        },
                        elements: {
                        line: {
                                borderWidth: 3
                        }
                        }
                }
                });
        </script>
        </body>
        </html>
        """

Example_2_inforgraphic = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Benchmarks & Achievements</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Inter', sans-serif;
        }
        .slide-container {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background-color: #0a0a0f;
            color: white;
            overflow: hidden;
        }
        .bg-pattern {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.08;
            z-index: 1;
            object-fit: cover;
        }
        .content {
            position: relative;
            z-index: 10;
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            padding: 3rem 4rem;
        }
        .slide-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #60a5fa;
        }
        .data-content {
            display: flex;
            margin-top: 1rem;
            height: 480px;
        }
        .chart-container {
            width: 60%;
            height: 100%;
        }
        .stats-container {
            width: 40%;
            padding-left: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .stat-card {
            background-color: rgba(16, 23, 42, 0.6);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #60a5fa;
        }
        .stat-title {
            font-size: 1rem;
            color: #a0aec0;
            font-weight: 500;
        }
        .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            margin-top: 0.25rem;
            margin-bottom: 0.25rem;
        }
        .stat-desc {
            font-size: 0.9rem;
            color: #e2e8f0;
        }
        .highlight {
            color: #60a5fa;
            font-weight: 500;
        }
        .accent-block {
            position: absolute;
            z-index: 5;
        }
        .block-1 {
            width: 80px;
            height: 5px;
            background-color: #60a5fa;
            top: 120px;
            left: 70px;
        }
        .logo {
            position: absolute;
            bottom: 40px;
            right: 50px;
            font-weight: 700;
            font-size: 1.2rem;
            letter-spacing: 1px;
        }
        .tech-icon {
            position: absolute;
            z-index: 4;
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.1);
        }
        .benchmark-list {
            margin-top: 1rem;
        }
        .benchmark-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .benchmark-icon {
            width: 20px;
            color: #60a5fa;
            margin-right: 0.5rem;
        }
        .small-chart-container {
            width: 100%;
            height: 140px;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- Background elements -->
        <img src="https://images.unsplash.com/photo-1550751827-4bd374c3f58b?auto=format&fit=crop&w=1280&q=80" class="bg-pattern" alt="Tech Pattern">
        
        <div class="accent-block block-1"></div>
        
        <!-- Tech icons scattered across the slide -->
        <i class="tech-icon fas fa-microchip" style="top: 120px; left: 200px;"></i>
        <i class="tech-icon fas fa-brain" style="top: 320px; right: 250px;"></i>
        <i class="tech-icon fas fa-robot" style="bottom: 150px; left: 300px;"></i>
        <i class="tech-icon fas fa-network-wired" style="top: 200px; right: 400px;"></i>
        <i class="tech-icon fas fa-chart-line" style="bottom: 250px; right: 320px;"></i>
        
        <!-- Main content -->
        <div class="content">
            <h1 class="slide-title">Performance Benchmarks & Achievements</h1>
            
            <div class="data-content">
                <div class="chart-container">
                    <canvas id="benchmarkChart"></canvas>
                </div>
                
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-title">HUMANITY'S LAST EXAM</div>
                        <div class="stat-value">50.7<span class="highlight">%</span></div>
                        <div class="stat-desc">First AI to reach the 50% threshold on this frontier benchmark</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">ARC-AGI V2</div>
                        <div class="stat-value">15.9<span class="highlight">%</span></div>
                        <div class="stat-desc">Nearly double the performance of previous leading models</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-title">VENDING-BENCH PERFORMANCE</div>
                        <div class="small-chart-container">
                            <canvas id="vendingChart"></canvas>
                        </div>
                        <div class="stat-desc">Grok 4: $4,694 | Claude Opus: $2,077 | Human: $844</div>
                    </div>
                    
                    <div class="benchmark-list">
                        <div class="benchmark-item">
                            <i class="benchmark-icon fas fa-trophy"></i>
                            <span>USAMO'25: 61.9% (Leading position)</span>
                        </div>
                        <div class="benchmark-item">
                            <i class="benchmark-icon fas fa-code"></i>
                            <span>LiveCodeBench: State-of-the-art performance</span>
                        </div>
                        <div class="benchmark-item">
                            <i class="benchmark-icon fas fa-calculator"></i>
                            <span>HMMT & AIME'25: Top competitive math results</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="logo">
            <span class="highlight">x</span>AI
        </div>
    </div>
    
    <script>
        // Main benchmark comparison chart
        const benchmarkCtx = document.getElementById('benchmarkChart').getContext('2d');
        const benchmarkChart = new Chart(benchmarkCtx, {
            type: 'bar',
            data: {
                labels: ['Grok 4 Heavy', 'Claude Opus', 'GPT-4o', 'Human Baseline'],
                datasets: [{
                    label: 'Performance Score',
                    data: [88, 75, 72, 65],
                    backgroundColor: [
                        '#60a5fa',
                        '#818cf8',
                        '#a78bfa',
                        '#c4b5fd'
                    ],
                    borderRadius: 6,
                    barThickness: 40
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Comparative Model Performance (Normalized)',
                        color: '#e2e8f0',
                        font: {
                            family: 'Inter',
                            size: 16
                        },
                        padding: 20
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        },
                        ticks: {
                            color: '#a0aec0'
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#e2e8f0',
                            font: {
                                family: 'Inter',
                                weight: '500'
                            }
                        }
                    }
                }
            }
        });
        
        // Vending bench comparison chart
        const vendingCtx = document.getElementById('vendingChart').getContext('2d');
        const vendingChart = new Chart(vendingCtx, {
            type: 'bar',
            data: {
                labels: ['Grok 4', 'Claude Opus', 'Human'],
                datasets: [{
                    label: 'Net Worth ($)',
                    data: [4694, 2077, 844],
                    backgroundColor: [
                        '#60a5fa',
                        '#818cf8',
                        '#c4b5fd'
                    ],
                    borderRadius: 4,
                    barThickness: 20
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#a0aec0',
                            font: {
                                size: 10
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        },
                        ticks: {
                            color: '#a0aec0',
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
"""

######################### Slide Title Example ############################################

Example_1_title = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grok 4 - Next Generation AI</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Inter', sans-serif;
        }
        .slide-container {
            width: 1280px;
            min-height: 720px;
            position: relative;
            background-color: #0a0a0f;
            color: white;
            overflow: hidden;
        }
        .bg-grid {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.15;
            z-index: 1;
        }
        .content {
            position: relative;
            z-index: 10;
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }
        .title {
            font-size: 5.5rem;
            font-weight: 800;
            letter-spacing: -1px;
            margin-bottom: 0.5rem;
            background: -webkit-linear-gradient(45deg, #60a5fa, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            font-size: 1.75rem;
            font-weight: 300;
            margin-bottom: 2rem;
        }
        .description {
            font-size: 1.25rem;
            max-width: 800px;
            text-align: center;
            font-weight: 300;
            line-height: 1.6;
            color: #a0aec0;
        }
        .accent-circle {
            position: absolute;
            border-radius: 50%;
            z-index: 5;
        }
        .circle-1 {
            width: 500px;
            height: 500px;
            top: -100px;
            right: -100px;
            background-color: #1a365d;
            opacity: 0.5;
        }
        .circle-2 {
            width: 300px;
            height: 300px;
            bottom: -50px;
            left: -50px;
            background-color: #3182ce;
            opacity: 0.3;
        }
        .tech-icon {
            position: absolute;
            z-index: 8;
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.15);
        }
        .xai-logo {
            margin-top: 3rem;
            font-weight: 700;
            font-size: 1.5rem;
            letter-spacing: 1px;
        }
        .highlight {
            color: #60a5fa;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- Background elements -->
        <img src="https://images.unsplash.com/photo-1557683304-673a23048d34?auto=format&fit=crop&w=1280&q=80" class="bg-grid" alt="Tech Grid Pattern">
        
        <div class="accent-circle circle-1"></div>
        <div class="accent-circle circle-2"></div>
        
        <!-- Tech icons scattered across the slide -->
        <i class="tech-icon fas fa-microchip" style="top: 120px; left: 200px;"></i>
        <i class="tech-icon fas fa-brain" style="top: 320px; right: 250px;"></i>
        <i class="tech-icon fas fa-robot" style="bottom: 150px; left: 300px;"></i>
        <i class="tech-icon fas fa-network-wired" style="top: 200px; right: 400px;"></i>
        <i class="tech-icon fas fa-code" style="bottom: 250px; right: 320px;"></i>
        
        <!-- Main content -->
        <div class="content">
            <div class="title">GROK 4</div>
            <div class="subtitle">NEXT GENERATION AI</div>
            <div class="description">
                A new era in artificial intelligence. Powered by xAI.
            </div>
            <div class="description mt-6">
                Experience the future: unrivaled reasoning, multimodal understanding, and state-of-the-art performance — brought to life in a stunning, modern interface.
            </div>
            <div class="xai-logo">
                <span class="highlight">x</span>AI
            </div>
        </div>
    </div>
</body>
</html>
"""

Example_2_title = """<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&display=swap">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <title>MCPサーバーの解説</title>
    <style>
        body {
            font-family: 'Noto Sans JP', sans-serif;
            background-color: #f8fafc;
            color: #1e293b;
            overflow: hidden;
        }
        .slide-container {
            width: 1280px;
            min-height: 720px;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .title-box {
            max-width: 80%;
            text-align: center;
        }
        .accent-line {
            width: 100px;
            height: 4px;
            background-color: #3b82f6;
            margin: 20px auto;
        }
        .tech-icon {
            position: absolute;
            opacity: 0.06;
            z-index: 0;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- Background tech icons for visual interest -->
        <i class="fas fa-server tech-icon text-9xl" style="top: 10%; left: 10%"></i>
        <i class="fas fa-network-wired tech-icon text-9xl" style="bottom: 15%; right: 10%"></i>
        <i class="fas fa-cloud tech-icon text-9xl" style="top: 20%; right: 20%"></i>
        <i class="fas fa-code-branch tech-icon text-9xl" style="bottom: 20%; left: 15%"></i>
        
        <div class="title-box z-10">
            <h1 class="text-5xl font-bold text-blue-800 mb-4">MCPサーバーの解説</h1>
            <div class="accent-line"></div>
            <h2 class="text-3xl text-gray-600 mt-4">〜概要と活用方法〜</h2>
            <p class="mt-16 text-lg text-gray-600">
                本プレゼンテーションでは、MCPサーバーとは何か、その基本概念、主な利用用途、有名なオープンソースツールやフレームワーク、将来展望について解説します。
            </p>
        </div>
        
        <div class="absolute bottom-8 right-8 text-sm text-gray-500">
            2025年7月14日
        </div>
    </div>
</body>
</html>
"""

######################### Agenda Example ############################################

Example_1_agenda = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Creative Design Workshop Agenda</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .slide-container {
            width: 1280px;
            min-height: 720px;
            background-color: #1a1a2e;
            position: relative;
            overflow: hidden;
            color: white;
        }
        .shape-1 {
            position: absolute;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background-color: #f72585;
            top: -100px;
            left: -100px;
            opacity: 0.7;
        }
        .shape-2 {
            position: absolute;
            width: 400px;
            height: 400px;
            border-radius: 50%;
            background-color: #4361ee;
            bottom: -200px;
            right: -150px;
            opacity: 0.6;
        }
        .shape-3 {
            position: absolute;
            width: 200px;
            height: 200px;
            background-color: #7209b7;
            transform: rotate(45deg);
            top: 50px;
            right: 100px;
            opacity: 0.5;
        }
        .agenda-item {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            position: relative;
        }
        .number-badge {
            font-size: 22px;
            font-weight: 700;
            color: #1a1a2e;
            background-color: #fff;
            border-radius: 12px;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            position: relative;
            z-index: 2;
        }
        .agenda-text {
            font-size: 22px;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }
        .content-area {
            position: relative;
            z-index: 2;
        }
        .agenda-item:nth-child(1) .number-badge {
            background-color: #f72585;
        }
        .agenda-item:nth-child(2) .number-badge {
            background-color: #7209b7;
        }
        .agenda-item:nth-child(3) .number-badge {
            background-color: #3a0ca3;
        }
        .agenda-item:nth-child(4) .number-badge {
            background-color: #4361ee;
        }
        .agenda-item:nth-child(5) .number-badge {
            background-color: #4cc9f0;
        }
        .pattern-dots {
            position: absolute;
            width: 200px;
            height: 200px;
            top: 50%;
            left: 30%;
            opacity: 0.3;
            z-index: 1;
        }
        .dot {
            width: 8px;
            height: 8px;
            background-color: white;
            border-radius: 50%;
            position: absolute;
        }
        .subtitle-accent {
            display: inline-block;
            padding: 4px 12px;
            background-color: #4cc9f0;
            border-radius: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- Background shapes -->
        <div class="shape-1"></div>
        <div class="shape-2"></div>
        <div class="shape-3"></div>
        
        <!-- Pattern dots -->
        <div class="pattern-dots">
            <div class="dot" style="top: 0; left: 0;"></div>
            <div class="dot" style="top: 0; left: 20px;"></div>
            <div class="dot" style="top: 0; left: 40px;"></div>
            <div class="dot" style="top: 0; left: 60px;"></div>
            <div class="dot" style="top: 20px; left: 0;"></div>
            <div class="dot" style="top: 20px; left: 20px;"></div>
            <div class="dot" style="top: 20px; left: 40px;"></div>
            <div class="dot" style="top: 20px; left: 60px;"></div>
            <div class="dot" style="top: 40px; left: 0;"></div>
            <div class="dot" style="top: 40px; left: 20px;"></div>
            <div class="dot" style="top: 40px; left: 40px;"></div>
            <div class="dot" style="top: 40px; left: 60px;"></div>
        </div>
        
        <div class="content-area p-16">
            <div class="mb-14">
                <div class="subtitle-accent text-sm">WORKSHOP SCHEDULE</div>
                <h1 class="text-5xl font-bold mb-2">Creative Design Workshop</h1>
                <h2 class="text-2xl font-light text-gray-300">Agenda</h2>
            </div>
            
            <div class="agenda-items mt-16">
                <div class="agenda-item">
                    <div class="number-badge">01</div>
                    <div class="agenda-text">Opening Inspiration</div>
                </div>
                
                <div class="agenda-item">
                    <div class="number-badge">02</div>
                    <div class="agenda-text">Color Theory Session</div>
                </div>
                
                <div class="agenda-item">
                    <div class="number-badge">03</div>
                    <div class="agenda-text">Hands-On Activities</div>
                </div>
                
                <div class="agenda-item">
                    <div class="number-badge">04</div>
                    <div class="agenda-text">Group Critiques</div>
                </div>
                
                <div class="agenda-item">
                    <div class="number-badge">05</div>
                    <div class="agenda-text">Closing Remarks</div>
                </div>
            </div>
            
            <div class="absolute bottom-8 right-16 text-sm text-gray-300">
                <div class="flex items-center">
                    <i class="fas fa-paint-brush mr-2"></i>
                    <span>July 17, 2025</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
"""

Example_2_agenda = """<!DOCTYPE html>

<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Report Index</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .slide-container {
            width: 1280px;
            min-height: 720px;
            background-color: #0a1929;
            position: relative;
            overflow: hidden;
            color: white;
        }
        .grid-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://cdn.jsdelivr.net/gh/hexagoncircle/tailwind-globe@main/images/grid.svg');
            background-size: cover;
            opacity: 0.07;
        }
        .blue-glow {
            position: absolute;
            width: 300px;
            height: 300px;
            background-color: #0a3d62;
            filter: blur(120px);
            top: -50px;
            left: -50px;
            opacity: 0.3;
            z-index: 1;
        }
        .gold-glow {
            position: absolute;
            width: 350px;
            height: 350px;
            background-color: #c5a45c;
            filter: blur(150px);
            bottom: -100px;
            right: -100px;
            opacity: 0.2;
            z-index: 1;
        }
        .content-area {
            position: relative;
            z-index: 2;
        }
        .agenda-item {
            display: flex;
            align-items: center;
            margin-bottom: 28px;
            position: relative;
            transition: all 0.3s ease;
        }
        .item-number {
            font-size: 18px;
            font-weight: 700;
            color: #0a1929;
            min-width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            position: relative;
            z-index: 2;
            border-radius: 6px;
        }
        .agenda-text {
            font-size: 20px;
            font-weight: 500;
            position: relative;
            z-index: 2;
            letter-spacing: 0.5px;
        }
        .tech-line {
            position: absolute;
            left: 22px;
            top: 44px;
            width: 2px;
            height: calc(100% - 20px);
            z-index: 1;
        }
        .title-accent {
            height: 3px;
            width: 60px;
            margin-bottom: 12px;
        }
        .title-tag {
            display: inline-flex;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: 500;
            letter-spacing: 1px;
            margin-bottom: 8px;
            border: 1px solid rgba(255,255,255,0.15);
            background-color: rgba(197, 164, 92, 0.1);
        }
        .chart-icon {
            position: absolute;
            right: 60px;
            top: 120px;
            width: 180px;
            height: 180px;
            border: 1px solid rgba(197, 164, 92, 0.3);
            background-color: rgba(197, 164, 92, 0.05);
            border-radius: 50%;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .chart-icon i {
            font-size: 70px;
            color: rgba(197, 164, 92, 0.2);
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- Background elements -->
        <div class="grid-pattern"></div>
        <div class="blue-glow"></div>
        <div class="gold-glow"></div>
        <div class="chart-icon">
            <i class="fas fa-chart-pie"></i>
        </div>
        
        <div class="content-area p-16">
            <div class="mb-12">
                <div class="title-tag text-xs text-yellow-300">FISCAL YEAR 2025</div>
                <h1 class="text-5xl font-bold mb-2 text-white">Financial Report Index</h1>
                <div class="title-accent bg-yellow-400"></div>
                <p class="text-gray-400 text-lg mt-4">Comprehensive overview of our financial performance</p>
            </div>
            
            <div class="agenda-items mt-14">
                <div class="agenda-item">
                    <div class="item-number bg-yellow-400">01</div>
                    <div class="agenda-text text-white">Executive Summary</div>
                    <div class="tech-line bg-yellow-400"></div>
                </div>
                
                <div class="agenda-item">
                    <div class="item-number bg-blue-500">02</div>
                    <div class="agenda-text text-white">Financial Statements</div>
                    <div class="tech-line bg-blue-500"></div>
                </div>
                
                <div class="agenda-item">
                    <div class="item-number bg-yellow-400">03</div>
                    <div class="agenda-text text-white">Key Metrics</div>
                    <div class="tech-line bg-yellow-400"></div>
                </div>
                
                <div class="agenda-item">
                    <div class="item-number bg-blue-500">04</div>
                    <div class="agenda-text text-white">Risk Assessment</div>
                    <div class="tech-line bg-blue-500"></div>
                </div>
                
                <div class="agenda-item">
                    <div class="item-number bg-yellow-400">05</div>
                    <div class="agenda-text text-white">Outlook</div>
                </div>
            </div>
            
            <div class="absolute bottom-10 left-16 text-sm text-gray-400">
                <div class="flex items-center">
                    <i class="fas fa-landmark mr-2"></i>
                    <span>Confidential • For Board & Investors Only</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>"""

######################### Slide Content Oultine Example ############################################

Example_1_outline = """
## **Building a Winning Startup Culture**

**Slide 1**: Building a Winning Startup Culture  
- Fostering innovation and accountability  
- By DuyQD, July 21, 2025

**Slide 2**: Set the Vision Early  
- Align the team around purpose  
- Make values actionable, not abstract

**Slide 3**: Empower Through Trust  
- Give ownership, not micromanagement  
- Encourage initiative and experimentation

**Slide 4**: Celebrate & Reflect  
- Recognize wins, learn from losses  
- Build rituals that reinforce culture

**Slide Final**: Thank you for your attention  
- Questions?
"""

Example_2_outline = """
## **Mastering Personal Productivity**

**Slide 1**: Mastering Personal Productivity  
- Simple habits for better focus and output  
- By Quan Duy, July 21, 2025

**Slide 2**: Start with Clear Goals  
- Define what success looks like each day  
- Break big tasks into focused actions

**Slide 3**: Control Your Time  
- Use time blocks for deep work  
- Limit meetings and interruptions

**Slide 4**: Build Sustainable Energy  
- Prioritize sleep, nutrition, and movement  
- Take intentional breaks to recharge

**Slide Final**: Thank you for your attention  
- Questions?

"""
############################################ Brainstorming Example ############################################
Example_1_brainstorm = """
/* --- Google Color Palette --- */
:root {
  --google-blue: #4285F4;          /* Primary brand blue */
  --google-red: #DB4437;           /* Bold red for emphasis */
  --google-yellow: #F4B400;        /* Vibrant yellow for highlights */
  --google-green: #0F9D58;         /* Fresh green for success/positive */
  --google-blue-light: #E3F2FD;    /* Light blue backgrounds */
  --google-gray-50: #FAFAFA;       /* Ultra light gray background */
  --google-gray-600: #757575;      /* Medium gray for secondary text */
  --google-gray-900: #212121;      /* Dark gray for primary text */
  --pure-white: #FFFFFF;           /* Clean white */
}

/* --- Enhanced Typography for Better Visibility --- */

/* Base Font Settings */
body {
  font-family: 'Google Sans', 'Roboto', 'Inter', sans-serif;
  color: var(--google-gray-900);
  line-height: 1.5;
  font-weight: 400;
}

/* Main Presentation Title (H1) */
.title-h1 {
  font-family: 'Google Sans', 'Poppins', sans-serif;
  font-size: 4rem; /* ~64px - more prominent */
  font-weight: 700;
  color: var(--google-blue);
  margin-bottom: 2rem;
  text-align: center;
}

/* Section Titles/Subtitles (H2) */
.subtitle-h2 {
  font-family: 'Google Sans', 'Poppins', sans-serif;
  font-size: 2.75rem; /* ~44px - increased visibility */
  font-weight: 600;
  color: var(--google-gray-900);
  margin-bottom: 1.5rem;
}

/* Section Subheadings (H3) */
.subtitle-h3 {
  font-family: 'Google Sans', 'Poppins', sans-serif;
  font-size: 2rem; /* ~32px */
  font-weight: 500;
  color: var(--google-red);
  margin-bottom: 1rem;
}

/* Body Text - Significantly Larger for Better Readability */
.text-normal {
  font-size: 1.5rem; /* ~24px - much more readable from distance */
  font-weight: 400;
  color: var(--google-gray-900);
  line-height: 1.6;
}

/* Large Body Text for Key Points */
.text-large {
  font-size: 1.75rem; /* ~28px - for important content */
  font-weight: 500;
  color: var(--google-gray-900);
  line-height: 1.5;
}

/* Medium Text for Supporting Details */
.text-medium {
  font-size: 1.25rem; /* ~20px */
  font-weight: 400;
  color: var(--google-gray-600);
  line-height: 1.5;
}

/* Small Text - Still Readable */
.text-small {
  font-size: 1rem; /* ~16px - minimum readable size */
  font-weight: 400;
  color: var(--google-gray-600);
}

/* --- Google-Inspired Background Styles --- */

/* Clean Default Background */
.slide-background-default {
  background-color: var(--pure-white);
}

/* Light Blue Accent Background */
.slide-background-blue {
  background-color: var(--google-blue-light);
}

/* Subtle Gray Background */
.slide-background-gray {
  background-color: var(--google-gray-50);
}

/* Google Brand Gradient */
.slide-background-gradient {
  background: linear-gradient(
    135deg,
    var(--google-blue) 0%,
    var(--google-green) 35%,
    var(--google-yellow) 70%,
    var(--google-red) 100%
  );
  color: var(--pure-white); /* Ensure text is readable on gradient */
}

/* Multi-color Stripe Background */
.slide-background-stripe {
  background: linear-gradient(
    to right,
    var(--google-blue) 0% 25%,
    var(--google-red) 25% 50%,
    var(--google-yellow) 50% 75%,
    var(--google-green) 75% 100%
  );
  background-size: 100% 8px;
  background-repeat: no-repeat;
  background-position: top;
  background-color: var(--pure-white);
}

/* --- Enhanced Layout Container --- */
.slide-container {
  width: 1280px;
  height: 720px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem; /* Optimized padding */
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

/* Content Area with Better Spacing */
.content-area {
  max-width: 1000px; /* Prevent content from being too wide */
  width: 100%;
  text-align: left;
}

.content-area.center {
  text-align: center;
}

/* --- Improved List Styling --- */
ul, ol {
  font-size: 1.5rem; /* Match body text */
  line-height: 1.7;
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.75rem;
  color: var(--google-gray-900);
}

/* Bullet points with Google colors */
ul li::marker {
  color: var(--google-blue);
}

/* --- Enhanced Fixed Slide Title --- */
.fixed-slide-title {
  position: absolute; /* Changed from fixed to absolute */
  top: 1.5rem;
  left: 3rem;
  font-family: 'Google Sans', 'Poppins', sans-serif;
  font-size: 2.25rem; /* ~36px - more visible */
  font-weight: 600;
  color: var(--google-blue);
  z-index: 100;
  background: rgba(255, 255, 255, 0.9); /* Subtle background for readability */
  padding: 0.5rem 1rem;
  border-radius: 8px;
  backdrop-filter: blur(4px);
}

/* --- Accent Elements --- */

/* Highlight Box */
.highlight-box {
  background-color: var(--google-yellow);
  color: var(--google-gray-900);
  padding: 1.5rem;
  border-radius: 12px;
  font-size: 1.5rem;
  font-weight: 500;
  margin: 1.5rem 0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Call-to-Action Button Style */
.cta-button {
  background-color: var(--google-red);
  color: var(--pure-white);
  padding: 1rem 2rem;
  border-radius: 24px;
  font-size: 1.25rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-button:hover {
  background-color: #c23321;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(219, 68, 55, 0.3);
}

/* --- Spacing and Flow Improvements --- */
p, ul, ol, h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1.5rem;
}

/* Remove margin from last child */
.content-block > *:last-child,
.content-area > *:last-child {
  margin-bottom: 0;
}

/* Add breathing room between sections */
.section {
  margin-bottom: 3rem;
}

/* --- Color Utility Classes --- */
.text-blue { color: var(--google-blue); }
.text-red { color: var(--google-red); }
.text-green { color: var(--google-green); }
.text-yellow { color: var(--google-yellow); }
.text-white { color: var(--pure-white); }

.bg-blue { background-color: var(--google-blue); }
.bg-red { background-color: var(--google-red); }
.bg-green { background-color: var(--google-green); }
.bg-yellow { background-color: var(--google-yellow); }

/* --- Responsive Text Scaling --- */
@media screen and (max-width: 1280px) {
  .title-h1 { font-size: 3.5rem; }
  .subtitle-h2 { font-size: 2.5rem; }
  .text-normal { font-size: 1.375rem; }
  .text-large { font-size: 1.625rem; }
}
"""