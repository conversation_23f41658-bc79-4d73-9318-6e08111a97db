
const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();

    // SLIDE 1: Title Slide
    const slide1 = pptx.addSlide();
    slide1.background = { color: '333333' }; // Extracted from .slide-container background-color

    // TITLE
    slide1.addText('All About Guinea Pigs', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 36, color: 'FFFFFF', bold: true
    });

    // SUBTITLE
    slide1.addText('A Comprehensive Guide to Cavy Care and Companionship', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 18, color: 'E0E0E0', bold: true
    });

    // CONTENT 1
    slide1.addText('Presenter: [Your Name/Company Name]', {
        x: 0.5, y: 2.5, w: 9.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 2
    slide1.addText('Date: [Current Date]', {
        x: 0.5, y: 3.2, w: 9.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // SLIDE 2: Understanding Guinea Pigs - Overview
    const slide2 = pptx.addSlide();
    slide2.background = { color: '333333' };

    // TITLE
    slide2.addText('Understanding Guinea Pigs', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, color: 'FFFFFF', bold: true
    });

    // CONTENT 1: H3 Overview
    slide2.addText('Overview', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 16, color: 'ADD8E6', bold: true
    });

    // CONTENT 2: Bullet 1
    slide2.addText('* Small, social rodents (Cavies).', {
        x: 0.5, y: 2.5, w: 9.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 3: Bullet 2
    slide2.addText('* Originate from Andes Mountains.', {
        x: 0.5, y: 3.2, w: 9.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 4: Bullet 3
    slide2.addText('* Known for docile nature.', {
        x: 0.5, y: 3.9, w: 9.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // SLIDE 3: Understanding Guinea Pigs - Characteristics
    const slide3 = pptx.addSlide();
    slide3.background = { color: '333333' };

    // TITLE
    slide3.addText('Understanding Guinea Pigs', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, color: 'FFFFFF', bold: true
    });

    // CONTENT 1: H3 Characteristics
    slide3.addText('Characteristics', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 16, color: 'ADD8E6', bold: true
    });

    // CONTENT 2: Bullet 1 (Left Column)
    slide3.addText('* Vocalizations: \'Wheeking\', \'purring\', \'rumbling\'.', {
        x: 0.5, y: 2.5, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 3: Bullet 2 (Left Column)
    slide3.addText('* Diet: Strict herbivores, constant fiber.', {
        x: 0.5, y: 3.2, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 4: Bullet 3 (Right Column)
    slide3.addText('* Companionship: Thrive in pairs/groups.', {
        x: 5.25, y: 2.5, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 5: Bullet 4 (Right Column)
    slide3.addText('* Lifespan: Typically 5-7 years.', {
        x: 5.25, y: 3.2, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // SLIDE 4: Understanding Guinea Pigs - Care Essentials (Part 1)
    const slide4 = pptx.addSlide();
    slide4.background = { color: '333333' };

    // TITLE
    slide4.addText('Understanding Guinea Pigs', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, color: 'FFFFFF', bold: true
    });

    // CONTENT 1: H3 Care Essentials
    slide4.addText('Care Essentials', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 16, color: 'ADD8E6', bold: true
    });

    // CONTENT 2: Bullet 1 (Left Column)
    slide4.addText('* Spacious Cage: Min 7.5 sq ft for two.', {
        x: 0.5, y: 2.5, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 3: Bullet 2 (Left Column)
    slide4.addText('* Daily Fresh Hay: Crucial for digestion.', {
        x: 0.5, y: 3.2, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 4: Bullet 3 (Right Column)
    slide4.addText('* Fresh Vegetables: Daily leafy greens.', {
        x: 5.25, y: 2.5, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 5: Bullet 4 (Right Column)
    slide4.addText('* Pellets: Small amount, fortified with Vit C.', {
        x: 5.25, y: 3.2, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // SLIDE 5: Understanding Guinea Pigs - Care Essentials (Part 2) & Behavior
    const slide5 = pptx.addSlide();
    slide5.background = { color: '333333' };

    // TITLE
    slide5.addText('Understanding Guinea Pigs', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, color: 'FFFFFF', bold: true
    });

    // CONTENT 1: Bullet 5 (Left Column)
    slide5.addText('* Vitamin C: Daily supplementation vital.', {
        x: 0.5, y: 1.8, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 2: Bullet 6 (Left Column)
    slide5.addText('* Fresh Water: Constant access, changed daily.', {
        x: 0.5, y: 2.5, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 3: H3 Behavior (Right Column)
    slide5.addText('Behavior', {
        x: 5.25, y: 1.8, w: 4.0, h: 0.6,
        fontSize: 16, color: 'ADD8E6', bold: true
    });

    // CONTENT 4: Bullet 1 (Right Column)
    slide5.addText('* Highly Social: Enjoy interaction.', {
        x: 5.25, y: 2.5, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 5: Bullet 2 (Right Column)
    slide5.addText('* Enrichment: Tunnels, hidey houses, chew toys.', {
        x: 5.25, y: 3.2, w: 4.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // SLIDE 6: Understanding Guinea Pigs - Behavior (Cont.)
    const slide6 = pptx.addSlide();
    slide6.background = { color: '333333' };

    // TITLE
    slide6.addText('Understanding Guinea Pigs', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, color: 'FFFFFF', bold: true
    });

    // CONTENT 1: Bullet 3
    slide6.addText('* Popcorning: Sign of happiness.', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    // CONTENT 2: Bullet 4
    slide6.addText('* Daily Floor Time: Supervised out-of-cage.', {
        x: 0.5, y: 2.5, w: 9.0, h: 0.6,
        fontSize: 14, color: 'F0F0F0'
    });

    return pptx.writeFile({ fileName: 'presentation_20250714_224303.pptx' });
}

// Execute the presentation creation
createPresentation()
    .then(() => {
        console.log('PowerPoint file generated successfully!');
        console.log('File saved as: presentation_20250714_224303.pptx');
        process.exit(0);
    })
    .catch(error => {
        console.error('Error generating presentation:', error);
        process.exit(1);
    });
