from openai import OpenAI
import async<PERSON> # Crucial for running blocking API calls in a thread
from typing import Dict, Any, Optional

class Kimik2_LLM:
    def __init__(self, api_key: str, model: str = "moonshotai/kimi-k2:free", temperature: float = 0.7,
                 base_url: str = "https://openrouter.ai/api/v1",
                 site_url: Optional[str] = None, site_name: Optional[str] = None):
        """
        Initializes the OpenRouter_LLM client.

        Args:
            api_key (str): Your OpenRouter API key.
            model (str): The model to use (default: "moonshotai/kimi-k2:free").
            temperature (float): The sampling temperature (default: 0.7).
            base_url (str): The base URL for the OpenRouter API (default: "https://openrouter.ai/api/v1").
            site_url (Optional[str]): Optional. Your site URL for rankings on openrouter.ai.
            site_name (Optional[str]): Optional. Your site title for rankings on openrouter.ai.
        """
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.base_url = base_url
        self.site_url = site_url
        self.site_name = site_name

        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key,
        )

        self.extra_headers = {}
        if self.site_url:
            self.extra_headers["HTTP-Referer"] = self.site_url
        if self.site_name:
            self.extra_headers["X-Title"] = self.site_name

    async def call(self, query: str, **kwargs: Any) -> Dict[str, Any]:
        """
        Makes an asynchronous call to the OpenRouter API for chat completions.

        Args:
            query (str): The user's prompt.
            **kwargs (Any): Additional keyword arguments to pass to the
                            client.chat.completions.create method.

        Returns:
            Dict[str, Any]: A dictionary containing the model's response,
                            typically {'text': <generated_text>}.
        """
        messages = [
            {"role": "user", "content": query}
        ]

        # Combine configured extra_headers with any headers passed in kwargs
        combined_extra_headers = {**self.extra_headers, **kwargs.pop('extra_headers', {})}

        # The actual API call is synchronous, so run it in a separate thread.
        try:
            completion = await asyncio.to_thread(
                self.client.chat.completions.create,
                model=self.model,
                messages=messages,
                temperature=self.temperature,
                extra_headers=combined_extra_headers,
                # extra_body={}, # You can include this if needed, or allow it via kwargs
                **kwargs # Allow other parameters like max_tokens, stop, etc.
            )
            return {'text': completion.choices[0].message.content}
        except Exception as e:
            # You might want more sophisticated error handling here
            print(f"Error calling OpenRouter API: {e}")
            return {'error': str(e)}