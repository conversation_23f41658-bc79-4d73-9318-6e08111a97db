import textwrap

##########################-Planner.py-###########################################
plannerpy_official_brainstorm_prompt = textwrap.dedent(
    """You are tasked to choose color for a presentation about "{query}" what will you choose for

    0. A suitable color Pallete
    1. Title, subtitle, text Size, Font & Color 
    2. Background color, Background Image (If applicable) # Use image_tavily to search for this. Choose the appropriate links based on description.
    4. Visualization chart, inforgraphic using chart.js (Simple and clean is better than complicated)
    5. Pictures using unsplash (If applicable) # Use image_tavily to search for this. Choose the appropriate links based on description.
    6. Icons using Font Awesome (Creative use)
    7. Logos using only 1 Wiki public Link. For example: https://upload.wikimedia.org/wikipedia/commons/a/a8/Microsoft_Azure_Logo.svg # Use image_tavily to search for this. Choose the appropriate links based on description.

    Beyond all your mission is to make this BEAUTIFUL. Answer this concise and to the point.
"""
)
plannerpy_official_outline_prompt = textwrap.dedent(
            """
           You are professional presentation planner, and you have been given the following request:

                "Make a presentation about"{query}""

                Create a structured outline for a presentation based on this request. 
                Slides need to be concise, visually appealing, 
                and guide the audience through your narrative, not just dump information.
                Refine it to be more consistent and flow like a proper presentation
                Format your response like this:
                {template_slides}

                🔒 Strict Constraints:
                If the request specifies a slide count (e.g., "1 slide", "3 slides", "single slide") — you must create exactly that many. No more, no less.

                If the Request dont specify a slide count, you must create 5 slides.

                Always structure the outline in pure Markdown — no extra commentary or explanation.

                Follow this slide format exactly:

                ##**Presentation's name**

                **Slide 1**: Title Slide

                **Slide 2**: Agenda / Index

                **Last Slide**: Thank you for your attention

                Make sure the slides title are matched with the Agenda / Index slide planned out.

                The outline should include:

                Constraint: Be short and concise this is an outline not a slide. Besides dont give too many points in just 1 slide its hard to present.
            """
        )
plannerpy_official_slide_content_prompt = textwrap.dedent(
            """
            You are a tech consultant working on the following client request:

            "{query}"

            Based on advice from a senior software engineer, you developed this slide outline:

            "{outline_response}"

            🛑 CRITICAL INSTRUCTIONS:
            You MUST follow the exact outline — no adding or removing slides.

            If the request limits the number of slides (e.g., "1 slide", "3 slides"), you MUST respect it.

            If no specific number of slides is mentioned, assume 5 slides.

                Slide 1 must always be the Title Slide

                Slide 2 must always be the Agenda / Index

                Create slide content only for the slides listed in the outline.

                Do NOT add extra slides or content, even if helpful.

                ✍️ Your Task:
                Write detailed content for each slide following the outline. For each slide:

                Be specific and concise — it’s a presentation, not a report.

                Do not overreach to unconventaional visuals (Complex HTML Drawing) unless explicitly instructed to do so.

                Stick to conventional visuals like bullet points, charts, and diagrams.
                If a diagram or image is needed (e.g., infrastructure diagram, org chart, GANTT chart), describe it in detail so it can be reproduced.

                Separate each slide using these tags:
                <Slide X START> and <Slide X END>, where X is the slide number.
            """
        )
############################-Generation.py-##########################################
generationpy_official_title_slide_prompt = textwrap.dedent(
        """
        You’re a professional creating a slide deck for:

        "{query}"

        A Professional Designer give you the following design for this Title Slide creation task as below::

        "{brain_storm}"

        Start by generating the Title Slide in full HTML and Tailwind CSS.

        🔧 Requirements:
        
        This is your master template slide, so design it with great care.
        
        You are provided a tool name "image_tavily" for images/logos/illustrations retrieval.

        Output only the HTML code — no explanations or comments.

        The designer had done his job
        now you only need to pay EXTREME ATTENTION to the position of the element inside the HTML. And CHOOSE THE RIGHT SIZE OF THE ELEMENTS

        # ALERT: AGAIN BE EXTREMELY CAREFUL ABOUT THE ELEMENT POSITIONS And CHOOSE THE RIGHT SIZE OF THE ELEMENTS

        📄 Slide content to include:
        {slide_content}

        Constraints:
        - Minimize the use of animations and transitions.
        - The slides MUST be 720p, and ensure all slide content fits within a height of 720px.
        - Output only the final HTML for the new slide.

        Beyond all your mission is to make this BEAUTIFUL
        """)
generationpy_official_agenda_slide_prompt = textwrap.dedent(
        """
        You’ve finished the title slide.
        Create the Agenda slide in full HTML and Tailwind CSS.

        A Professional Designer give you the following css for this Slide creation task as below::
        "{brain_storm}"

        Requirements
        - Conform to the design style of the Professional Designer.

        You are provided a tool name "image_tavily" for images/logos/illustrations retrieval.

        Output ONLY the complete HTML—nothing else.
        
        The designer had done his job
        now you only need to pay EXTREME ATTENTION to the position of the element inside the HTML. And CHOOSE THE RIGHT SIZE OF THE ELEMENTS

        # ALERT: AGAIN BE EXTREMELY CAREFUL ABOUT THE ELEMENT POSITIONS And CHOOSE THE RIGHT SIZE OF THE ELEMENTS
        Slide content to include
        {slide_content}

        Constraints:
        - Minimize the use of animations and transitions.
        - The slides MUST be 720p, and ensure all slide content fits within a height of 720px.
        - Output only the final HTML for the new slide.


        Beyond all your mission is to make this BEAUTIFUL
        """
        )
generationpy_official_general_slide_prompt = textwrap.dedent("""
        You are a tech consultant preparing a slide deck based on the following request:
        "{query}"

        A Professional Designer give you the following css for this Slide creation task as below::
        "{brain_storm}"

        Now you must generate the next slide in full HTML and Tailwind CSS., using the content defined here:
        {slide_content}
                                                   
        You are provided a tool name "image_tavily" for images/logos/illustrations retrieval.
                                                                             
        Instructions:
        Refer to the design style of the Professional Designer.
        Keep it visually appealing and functional.This is a presentation slide, so it should be engaging and easy to read. 
        Plan for illusatrations (Prioritize simple over complex) /visualizations (Which should prioritize conventional charts and graph over complex)
        Prioritize clarity and visual appeal. Especially conventionalvisualization like charts, graphs, and icons.
                                                   
        Again this is a presentation slides LESS WORDS IS BETTER dont write LONG ASS PARAGRAPH
        

        # ALERT: BE EXTREMELY CAREFUL ABOUT THE ELEMENT POSITIONS And CHOOSE THE RIGHT SIZE OF THE ELEMENTS
                                                   
        Constraints:
        - Minimize the use of animations and transitions.
        - The slides MUST be 720p, and ensure all slide content fits within a height of 720px.
        - Output only the final HTML for the new slide.
                                                   
        Beyond all your mission is to make this BEAUTIFUL
        
        """)
generationpy_official_slide_review_prompt = textwrap.dedent(
    """
        You are a senior front-end software engineer reviewing a junior engineer's work.
        He has written some HTML which is supposed to show one slide of a powerpoint.

        You have been provided with the HTML code and also a rendering of the code as an image.

        Look at the image then validate the following criteria.
        1. Make sure that text, visual elements and content blocks are completely contained within the slide and not cut off at the bottom of the slide. 
        If this criteria is not met, reduce the vertical padding/spacing between visual elements, titles, subtitles and content blocks OR reduce the font size of the text component to meet the criteria.
        You can reduce the padding by changing the padding or gap parameters, or the margin-bottom parameter of any titles.

        2. Make sure that visual elements do NOT overlap with each other e.g. the company logo overlaps with slide content.
        If anything is overlapping, MAKE SURE to reposition or adjust the size of the frontmost element.

        Do NOT make changes to the code if the above criteria is met.
        If code changes need to be made, only output the improved HTML code, do not output any other text.
        If the code meets all of the criteria, simply output <OK>.

        The HTML code is provided below:
        {code}
        """)