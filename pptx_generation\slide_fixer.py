import logging
from typing import List, Dict, Any
from llm.llmwrapper import LLM
from htmlrender.renderer import HTMLRenderer
from utils.utils import find_text_in_between_tags
from loguru import logger
from prompt.experiment_prompts import (
    overflow_fix_ultra_aggressive_prompt,
    reviewer_fix_overflow_safe_prompt,
    reviewer_fix_general_prompt,
    slide_fix_comprehensive_prompt,
    slide_regeneration_with_content_prompt
)
from pptx_generation.simple_overflow_checker import check_simple_overflow_async

logger = logging.getLogger(__name__)

class SlideFixer:
    """Handles slide fixing operations with proper async support and modular design"""
    
    def __init__(self):
        pass
    
    def parse_orchestrator_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the orchestrator's structured response"""
        result = {
            'tools_to_use': {'OVERFLOW_CHECKER': False, 'REVIEWER_FIX': False, 'CUSTOM_FIX': False},
            'execution_order': [],
            'reasoning': ''
        }

        try:
            # Extract TOOLS_TO_USE section
            if 'TOOLS_TO_USE:' in response_text:
                tools_section = response_text.split('TOOLS_TO_USE:')[1].split('EXECUTION_ORDER:')[0]
                for line in tools_section.strip().split('\n'):
                    if 'OVERFLOW_CHECKER:' in line and 'YES' in line:
                        result['tools_to_use']['OVERFLOW_CHECKER'] = True
                    elif 'REVIEWER_FIX:' in line and 'YES' in line:
                        result['tools_to_use']['REVIEWER_FIX'] = True
                    elif 'CUSTOM_FIX:' in line and 'YES' in line:
                        result['tools_to_use']['CUSTOM_FIX'] = True

            # Extract EXECUTION_ORDER section
            if 'EXECUTION_ORDER:' in response_text:
                order_section = response_text.split('EXECUTION_ORDER:')[1].split('REASONING:')[0]
                for line in order_section.strip().split('\n'):
                    line = line.strip()
                    if any(tool in line for tool in ['OVERFLOW_CHECKER', 'REVIEWER_FIX', 'CUSTOM_FIX']):
                        for tool in ['OVERFLOW_CHECKER', 'REVIEWER_FIX', 'CUSTOM_FIX']:
                            if tool in line:
                                result['execution_order'].append(tool)
                                break

            # Extract reasoning
            if 'REASONING:' in response_text:
                result['reasoning'] = response_text.split('REASONING:')[1].strip()

        except Exception as e:
            logger.warning(f"⚠️ Error parsing orchestrator response: {e}")
            # Fallback: if parsing fails, default to reviewer fix
            result['tools_to_use']['REVIEWER_FIX'] = True
            result['execution_order'] = ['REVIEWER_FIX']
            result['reasoning'] = 'Parsing failed, defaulting to reviewer fix'

        return result

    def is_error_slide(self, html_content: str) -> bool:
        """Check if the HTML content represents an error slide that needs regeneration"""

        # Core error indicators (original list)
        error_indicators = [
            "Error rendering slide",
            "Error generating slide",
            "RESOURCE_EXHAUSTED",
            "Error 429",
            "quota",
            "KeyError:",
            "Exception:",
            "Traceback",
            "Error: 'search'"
        ]

        # Additional error patterns (more comprehensive)
        additional_errors = [
            "error:",
            "failed",
            "timeout",
            "connection error",
            "network error",
            "api error",
            "rate limit",
            "service unavailable",
            "invalid api key",
            "authentication failed",
            "server error",
            "internal error",
            "bad request",
            "forbidden",
            "not found",
            "undefined",
            "null",
            "generation failed",
            "processing failed"
        ]

        # Combine all error indicators
        all_indicators = error_indicators + additional_errors

        # Convert to lowercase for case-insensitive matching
        html_lower = html_content.lower()

        # Check for error indicators
        has_error_text = any(indicator.lower() in html_lower for indicator in all_indicators)

        # Additional heuristics for error detection
        # 1. Very short HTML (likely an error message)
        is_too_short = len(html_content.strip()) < 500

        # 2. Missing essential slide elements (indicates incomplete generation)
        missing_elements = (
            'tailwindcss.com' not in html_content and
            'slide-container' not in html_content and
            len(html_content.strip()) < 2000
        )

        # 3. Red text styling (often used for errors)
        has_error_styling = 'color: red' in html_content or 'text-red' in html_content

        return has_error_text or (is_too_short and has_error_styling) or missing_elements

    def extract_style_context(self, style_reference_slides: List[Dict[str, Any]]) -> str:
        """Extract style information from reference slides for consistency"""
        if not style_reference_slides:
            return ""

        # Take the first 2-3 slides as style reference (avoid taking too many)
        reference_slides = style_reference_slides[:3]

        style_context = "STYLE CONSISTENCY REQUIREMENTS:\n"
        style_context += "Use these existing slides as COMPLETE TEMPLATES to copy from:\n\n"

        for i, slide in enumerate(reference_slides, 1):
            slide_html = slide.get('html_content', '')
            if slide_html and not self.is_error_slide(slide_html):
                style_context += f"COMPLETE REFERENCE SLIDE {slide.get('slide_number', i)} (copy this structure exactly):\n"
                style_context += slide_html + "\n\n"

        style_context += """
IMPORTANT: Your generated slide MUST maintain the same:
- Color scheme and palette
- Font families and sizes  
- Layout structure and spacing
- Visual design patterns
- CSS styling approach

Match the visual style exactly while implementing the new content.
"""
        return style_context

    async def execute_overflow_checker(self, html_content: str, llm_instance: LLM, style_reference_slides: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Execute overflow checking and fixing"""
        try:
            simple_result = await check_simple_overflow_async(html_content)
            if simple_result.get('has_overflow', False):
                logger.info(f"📊 Overflow detected: {simple_result['message']}")
                logger.info(f"💡 Recommendations: {', '.join(simple_result.get('recommendations', []))}")

                # Extract style context for overflow fix
                style_context = self.extract_style_context(style_reference_slides)

                # 🔍 DEBUG: Log the style context being used
                logger.info(f"📋 Style context length: {len(style_context)} characters")
                if style_context:
                    logger.info(f"📄 Style context preview: {style_context[:300]}...")
                else:
                    logger.warning("⚠️ No style context available for overflow fix")

                # Use template prompt with style context
                overflow_fix_prompt = overflow_fix_ultra_aggressive_prompt.format(
                    current_html=html_content,
                    style_context=style_context
                )

                try:
                    overflow_fix_response = await llm_instance.call(query=overflow_fix_prompt)
                    fixed_html = find_text_in_between_tags(
                        overflow_fix_response['text'],
                        start_tag="<!DOCTYPE html>",
                        end_tag="</html>",
                        inclusive=True
                    )

                    if fixed_html and fixed_html != html_content:
                        logger.info("✅ OVERFLOW_CHECKER applied (Content Fixed)")
                        return {
                            'success': True,
                            'fixed_html': fixed_html,
                            'tool_used': 'OVERFLOW_CHECKER_FIXED',
                            'overflow_fixed': True
                        }
                    else:
                        logger.warning("⚠️ OVERFLOW_CHECKER detected but couldn't fix content")
                        return {
                            'success': True,
                            'fixed_html': html_content,
                            'tool_used': 'OVERFLOW_CHECKER_SIMPLE',
                            'overflow_fixed': True
                        }
                except Exception as fix_e:
                    logger.error(f"❌ Overflow fix failed: {fix_e}")
                    return {
                        'success': True,
                        'fixed_html': html_content,
                        'tool_used': 'OVERFLOW_CHECKER_SIMPLE',
                        'overflow_fixed': True
                    }
            else:
                logger.info(f"ℹ️ Simple overflow analysis: {simple_result['message']}")
                return {
                    'success': True,
                    'fixed_html': html_content,
                    'tool_used': 'OVERFLOW_CHECKER_NONE',
                    'overflow_fixed': False
                }
        except Exception as e:
            logger.error(f"❌ Simple overflow checker failed: {e}")
            return {
                'success': False,
                'fixed_html': html_content,
                'tool_used': 'OVERFLOW_CHECKER_ERROR',
                'overflow_fixed': False,
                'error': str(e)
            }

    async def execute_reviewer_fix(self, html_content: str, user_instructions: str, llm_instance: LLM,
                                 overflow_already_fixed: bool = False, style_reference_slides: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Execute reviewer fix with proper style reference integration"""
        try:
            # Extract style context
            style_context = self.extract_style_context(style_reference_slides)

            if overflow_already_fixed:
                logger.info("⚠️ Overflow already fixed - using overflow-safe reviewer")

                # Use overflow-safe prompt template
                review_prompt = reviewer_fix_overflow_safe_prompt.format(
                    user_instructions=user_instructions,
                    current_html=html_content,
                    style_context=style_context
                )
                tool_suffix = "_OVERFLOW_SAFE"
            else:
                logger.info("🔄 Using general reviewer")

                # Use general reviewer prompt template
                review_prompt = reviewer_fix_general_prompt.format(
                    user_instructions=user_instructions,
                    current_html=html_content,
                    style_context=style_context
                )
                tool_suffix = "_GENERAL"

            # Execute LLM review
            llm_review = await llm_instance.call(query=review_prompt)
            improved_html = find_text_in_between_tags(
                llm_review['text'],
                start_tag="<!DOCTYPE html>",
                end_tag="</html>",
                inclusive=True
            )

            if improved_html and improved_html != html_content:
                logger.info(f"✅ REVIEWER_FIX applied{tool_suffix}")
                return {
                    'success': True,
                    'fixed_html': improved_html,
                    'tool_used': f'REVIEWER_FIX{tool_suffix}'
                }
            else:
                logger.info(f"ℹ️ REVIEWER_FIX: No changes made{tool_suffix}")
                return {
                    'success': True,
                    'fixed_html': html_content,
                    'tool_used': f'REVIEWER_FIX{tool_suffix}_NO_CHANGE'
                }

        except Exception as e:
            logger.error(f"❌ Reviewer fix failed: {e}")
            return {
                'success': False,
                'fixed_html': html_content,
                'tool_used': 'REVIEWER_FIX_ERROR',
                'error': str(e)
            }

    async def execute_custom_fix(self, html_content: str, user_instructions: str, llm_instance: LLM,
                               original_slide_content: str = None, user_prompt: str = None,
                               style_reference_slides: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Execute custom fix or regeneration for error slides"""
        try:
            # Check if this is an error slide that needs regeneration
            if self.is_error_slide(html_content):
                logger.info("🔄 Detected error slide - attempting regeneration")

                # Extract style context
                style_context = self.extract_style_context(style_reference_slides)

                if original_slide_content and user_prompt:
                    logger.info("📋 Using original slide content for regeneration")
                    regeneration_prompt = slide_regeneration_with_content_prompt.format(
                        user_prompt=user_prompt,
                        original_slide_content=original_slide_content,
                        style_context=style_context
                    )
                else:
                    # 🔧 FIX: For error slides, ignore user instructions and regenerate based on context
                    logger.info("⚠️ Original slide content not available, regenerating from context")

                    # Create a generic regeneration prompt that asks the LLM to create appropriate content
                    # based on the style references and presentation context
                    regeneration_prompt = f"""
You are a tech consultant creating a professional presentation slide.

A previous slide generation failed with an error. Please create a new slide that fits the presentation context.

STYLE CONSISTENCY REQUIREMENTS:
Use these existing slides as COMPLETE TEMPLATES to copy from:

{style_context}

INSTRUCTIONS:
1. Analyze the existing slides to understand the presentation topic and style
2. Create a slide that logically fits in this presentation sequence
3. Follow the exact same design patterns, colors, fonts, and structure as the reference slides
4. Use proper Tailwind CSS classes and maintain the slide-container structure
5. Ensure the slide content fits within a height of 720px
6. Make it visually appealing and professional

OUTPUT ONLY the complete HTML code for the new slide, no other text.
"""

                response = await llm_instance.call(query=regeneration_prompt)
                regenerated_html = find_text_in_between_tags(
                    response['text'],
                    start_tag="<!DOCTYPE html>",
                    end_tag="</html>",
                    inclusive=True
                )

                if regenerated_html:
                    logger.info("✅ Slide regenerated successfully")
                    return {
                        'success': True,
                        'fixed_html': regenerated_html,
                        'tool_used': 'REGENERATE_SLIDE'
                    }
                else:
                    logger.warning("⚠️ Regeneration failed - no valid HTML found")
                    return {
                        'success': False,
                        'fixed_html': html_content,
                        'tool_used': 'REGENERATE_SLIDE_FAILED'
                    }
            else:
                # Regular custom fix for non-error slides
                style_context = self.extract_style_context(style_reference_slides)

                # 🔧 ADDITIONAL CHECK: Even if not detected as error slide,
                # check if user is reporting an error and current HTML looks broken
                if any(word in user_instructions.lower() for word in ['error', 'broken', 'failed', 'not working', 'issue']):
                    # Double-check if this might be an error slide we missed
                    if (len(html_content.strip()) < 1000 or
                        'color: red' in html_content or
                        'undefined' in html_content.lower()):
                        logger.info("🔍 User reported error on suspicious HTML - treating as error slide")

                        # Treat as error slide regeneration
                        regeneration_prompt = f"""
You are a tech consultant creating a professional presentation slide.

The user reported an error with the current slide. Please create a new slide that fits the presentation context.

STYLE CONSISTENCY REQUIREMENTS:
Use these existing slides as COMPLETE TEMPLATES to copy from:

{style_context}

INSTRUCTIONS:
1. Analyze the existing slides to understand the presentation topic and style
2. Create a slide that logically fits in this presentation sequence
3. Follow the exact same design patterns, colors, fonts, and structure as the reference slides
4. Use proper Tailwind CSS classes and maintain the slide-container structure
5. Ensure the slide content fits within a height of 720px
6. Make it visually appealing and professional

OUTPUT ONLY the complete HTML code for the new slide, no other text.
"""

                        response = await llm_instance.call(query=regeneration_prompt)
                        regenerated_html = find_text_in_between_tags(
                            response['text'],
                            start_tag="<!DOCTYPE html>",
                            end_tag="</html>",
                            inclusive=True
                        )

                        if regenerated_html:
                            logger.info("✅ Error slide regenerated via fallback detection")
                            return {
                                'success': True,
                                'fixed_html': regenerated_html,
                                'tool_used': 'REGENERATE_SLIDE_FALLBACK'
                            }

                # Regular custom fix for non-error slides
                custom_prompt = slide_fix_comprehensive_prompt.format(
                    user_instructions=user_instructions,
                    current_html=html_content,
                    style_context=style_context
                )

                response = await llm_instance.call(query=custom_prompt)
                fixed_html = find_text_in_between_tags(
                    response['text'],
                    start_tag="<!DOCTYPE html>",
                    end_tag="</html>",
                    inclusive=True
                )

                if fixed_html and fixed_html != html_content:
                    logger.info("✅ CUSTOM_FIX applied")
                    return {
                        'success': True,
                        'fixed_html': fixed_html,
                        'tool_used': 'CUSTOM_FIX'
                    }
                else:
                    logger.info("ℹ️ CUSTOM_FIX: No changes made")
                    return {
                        'success': True,
                        'fixed_html': html_content,
                        'tool_used': 'CUSTOM_FIX_NO_CHANGE'
                    }

        except Exception as e:
            logger.error(f"❌ Custom fix failed: {e}")
            return {
                'success': False,
                'fixed_html': html_content,
                'tool_used': 'CUSTOM_FIX_ERROR',
                'error': str(e)
            }

    async def execute_slide_fix_tools(self, html_content: str, user_instructions: str, tools_to_execute: List[str],
                                    llm_instance: LLM, slide_number: int = None, original_slide_content: str = None,
                                    user_prompt: str = None, style_reference_slides: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Execute the selected tools in order with proper async support"""
        current_html = html_content
        tools_used = []
        overflow_already_fixed = False

        try:
            for tool in tools_to_execute:
                if tool == 'OVERFLOW_CHECKER':
                    logger.info("🔧 Executing OVERFLOW_CHECKER...")
                    result = await self.execute_overflow_checker(current_html, llm_instance, style_reference_slides)

                    if result['success']:
                        current_html = result['fixed_html']
                        tools_used.append(result['tool_used'])
                        overflow_already_fixed = result.get('overflow_fixed', False)
                    else:
                        tools_used.append(result['tool_used'])
                        logger.error(f"Overflow checker failed: {result.get('error', 'Unknown error')}")

                elif tool == 'REVIEWER_FIX':
                    logger.info("🔧 Executing REVIEWER_FIX...")
                    result = await self.execute_reviewer_fix(
                        current_html, user_instructions, llm_instance,
                        overflow_already_fixed, style_reference_slides
                    )

                    if result['success']:
                        current_html = result['fixed_html']
                        tools_used.append(result['tool_used'])
                    else:
                        tools_used.append(result['tool_used'])
                        logger.error(f"Reviewer fix failed: {result.get('error', 'Unknown error')}")

                elif tool == 'CUSTOM_FIX':
                    logger.info("🔧 Executing CUSTOM_FIX...")
                    result = await self.execute_custom_fix(
                        current_html, user_instructions, llm_instance,
                        original_slide_content, user_prompt, style_reference_slides
                    )

                    if result['success']:
                        current_html = result['fixed_html']
                        tools_used.append(result['tool_used'])
                    else:
                        tools_used.append(result['tool_used'])
                        logger.error(f"Custom fix failed: {result.get('error', 'Unknown error')}")

            return {
                'success': True,
                'fixed_html': current_html,
                'tools_used': tools_used
            }

        except Exception as e:
            logger.error(f"❌ Error executing tools: {e}")
            return {
                'success': False,
                'fixed_html': html_content,  # Return original on error
                'tools_used': tools_used,
                'error': str(e)
            }
