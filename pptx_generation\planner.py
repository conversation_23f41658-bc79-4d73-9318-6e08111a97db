import sys
from loguru import logger
from llm.llmwrapper import LLM
from utils.utils import find_text_in_between_tags
import textwrap
from prompt.plannerpy_outline_prompt import plannerpy_outline_prompt_duy_v2
from prompt.plannerpy_brainstorm_prompt import plannerpy_brainstorm_prompt_duy_v2
from prompt.plannerpy_slide_content_prompt import plannerpy_slide_content_prompt_duy_v1
from prompt.official_prompts import plannerpy_official_brainstorm_prompt, plannerpy_official_outline_prompt, plannerpy_official_slide_content_prompt
from llm.few_shot_template import Example_1_outline, Example_2_outline, Example_1_brainstorm

class Planner():

    # Make these methods async
    async def brainstorm(self, query : str, llm : LLM) -> str:

        brainstorm_prompt = plannerpy_official_brainstorm_prompt.format(
            query=query
            #, Example_1=Example_1_brainstorm
        )

        logger.info(
            textwrap.dedent(
                f"""PPTX Planning STEP 1: Brainstorming implementation approach based on user query...
                    QUERY: "{query}"
                """
            )
        )

        # AWAIT THE LLM CALL
        brainstorm_response = await llm.call(query=brainstorm_prompt)
        brainstorm_text = brainstorm_response['text'] # Extract text after awaiting
        
        # Extract and log token counts for brainstorm
        brainstorm_input_token_count = brainstorm_response['input_token_count']
        brainstorm_output_token_count = brainstorm_response.get('output_token_count', 0)

        logger.info("PPTX Planning STEP 1: Brainstorming complete!")
        logger.info(f"Brainstorm Input token count: {brainstorm_input_token_count}")
        logger.info(f"Brainstorm Output token count: {brainstorm_output_token_count}")

        return {'prompt' : brainstorm_prompt, 'response' : brainstorm_text} # Return the text


    # Make these methods async
    async def outline(self, query : str, llm : LLM) -> str:
        # Extract slide count from query if specified
        slide_count = self._extract_slide_count_from_query(query)

        # Generate dynamic template based on requested slide count
        template_slides = self._generate_slide_template(slide_count)
        #example_slides = self._generate_example_slides(slide_count) # This one is overlapped with few shot templates

        outline_prompt = plannerpy_official_outline_prompt.format(
                                query=query,
                                template_slides=template_slides,
                                #example_slides=example_slides, # This one is overlapped with few shot templates
                                #Example_1 = Example_1_outline, Example_2 = Example_2_outline
                            )


        logger.info("PPTX Planning STEP 2: Creating high-level Presentation outline...")

        # AWAIT THE LLM CALL
        outline_response = await llm.call(query=outline_prompt)
        outline_text = outline_response['text'] # Extract text after awaiting
        outline_input_token_count = outline_response['input_token_count'] # Extract token count
        outline_output_token_count = outline_response.get('output_token_count', 0) # Extract output token count if available

        logger.info("PPTX Planning STEP 2: Outline creation complete!")
        logger.info(f"Outline Input token count: {outline_input_token_count}")
        logger.info(f"Outline Output token count: {outline_output_token_count}")

        return {'prompt' : outline_prompt, 'response' : outline_text} # Return the text


    # Make these methods async
    async def slide_content(self, query : str, outline_response : str, llm : LLM) -> str:
        slide_content_prompt = plannerpy_official_slide_content_prompt.format(query=query, outline_response=outline_response)

        logger.info("PPTX Planning STEP 3: Defining the content to appear on each slide...")

        # AWAIT THE LLM CALL
        slide_content_response = await llm.call(query=slide_content_prompt)
        slide_content_text = slide_content_response['text'] # Extract text after awaiting

        # Extract and log token counts for slide_content
        slide_content_input_token_count = slide_content_response['input_token_count']
        slide_content_output_token_count = slide_content_response.get('output_token_count', 0)


        logger.info("PPTX Planning STEP 3: Slide content creation complete!")
        logger.info(f"Slide Content Input token count: {slide_content_input_token_count}")
        logger.info(f"Slide Content Output token count: {slide_content_output_token_count}")

        return {'prompt' : slide_content_prompt, 'response' : slide_content_text} # Return the text


    def extract_flags(self, text: str, startflag : str = '<', endflag : str = '>' ) -> list:
        # This is a synchronous helper, it's fine
        """
        Get all <Slide 1 START> <Slide 1 END> Flags
        """
        flags = []
        start = 0
        while True:
            start = text.find(startflag, start)
            if start == -1:
                break
            end = text.find(endflag, start)
            if end == -1:
                break

            flags.append(text[start:end+1])
            start = end + 1

        if len(flags) % 2 != 0:
            raise Exception(f"Uneven number of separation flags. Start and End flags come in pairs")

        return flags

    def extract_slide_content(self, slide_content_response : str) -> dict:
        # This is a synchronous helper, it's fine
        logger.info("PPTX Planning STEP 4: Extracting slide content...")

        flags = self.extract_flags(slide_content_response)

        template_master, content = [], {}
        slidenum = 1

        for i in range(0,len(flags),2):

            slide_content = find_text_in_between_tags(slide_content_response, flags[i], flags[i + 1])
            content[f"slide_{slidenum}"] = slide_content

            if slidenum in (1,2) or 'conclusion' in slide_content.lower():
                template_master.append(f"slide_{slidenum}")
            slidenum += 1

        logger.info("PPTX Planning STEP 4: Slide content extracted!")
        return {'slide_content' : content, 'template_slides' : template_master}

    # Make the main plan_content method async
    async def plan_content(self, query : str, llm_1 : LLM, llm_2 : LLM = None, llm_3 : LLM = None) -> dict:
        """
        Main planning function
        """

        brainstorm_llm = llm_1
        outline_llm = llm_2 if llm_2 is not None else llm_1
        slide_content_llm = llm_3 if llm_3 is not None else llm_1

        # 1. Brainstorm - AWAIT THE CALL
        brainstorm_output = await self.brainstorm(query=query, llm=brainstorm_llm)

        # 2. Create high-level slide outline - AWAIT THE CALL
        outline_output = await self.outline(query=query, llm=outline_llm)

        # 3. Create detailed slide content at a slide granularity - AWAIT THE CALL
        slide_content_output = await self.slide_content(query=query,
                                                        brainstorm_response=brainstorm_output['response'],
                                                        outline_response=outline_output['response'],
                                                        llm=slide_content_llm
                                                        )

        # 4. Extract slide content
        processed_slide_content_output = self.extract_slide_content(slide_content_response=slide_content_output['response'])

        return {'brainstorm' : brainstorm_output,
                'outline' : outline_output,
                'unprocessed_slide_content' : slide_content_output,
                'processed_slide_content' : processed_slide_content_output
                }

    # These are fine as synchronous methods
    def _extract_slide_count_from_query(self, query: str) -> int:
        """Extract the requested number of slides from the user query"""
        import re

        # Convert to lowercase for easier matching
        query_lower = query.lower()

        # Look for patterns like "1 slide", "3 slides", "single slide", "one slide"
        patterns = [
            r'(\d+)\s+slides?',  # "3 slides", "1 slide"
            r'(one|single)\s+slide',  # "one slide", "single slide"
            r'only\s+(\d+)\s+slides?',  # "only 1 slide"
            r'just\s+(\d+)\s+slides?',  # "just 2 slides"
        ]

        for pattern in patterns:
            match = re.search(pattern, query_lower)
            if match:
                number_str = match.group(1)
                if number_str in ['one', 'single']:
                    return 1
                try:
                    return int(number_str)
                except ValueError:
                    continue

        # Default to 5 slides if no specific count is mentioned
        return 5

    def _generate_slide_template(self, slide_count: int) -> str:
        """Generate the slide template based on requested count"""
        template = ""
        for i in range(1, slide_count + 1):
            template = f"""
## **Presentation's name**

**Slide 1**: Slide 1 Title
- Subtitle or description
- Author, Date

**Slide 2**: Slide 2 Title
- Bullet point 1
- Bullet point 2

**Slide {{i}}**: Slide {{i}} Title
- Bullet point 1
- Bullet point 2
**Slide Final**: Thank you for your attention
- Questions?
"""
        return template.strip()

    def _generate_example_slides(self, slide_count: int) -> str:
        """Generate example slides based on requested count"""
        if slide_count == 1:
            return """**Slide 1**: Website Redesign Proposal
                - Complete redesign with modern UI
                - Jane Doe, March 2025"""

        elif slide_count == 2:
            return """**Slide 1**: Title
            - Website Redesign Proposal
            - Jane Doe, March 2025

            **Slide 2**: Solution & Next Steps
            - Modern UI with improved navigation
            - Design prototype and user testing"""

        elif slide_count == 3:
            return """**Slide 1**: Title
            - Website Redesign Proposal
            - Jane Doe, March 2025

            **Slide 2**: Challenge & Solution
            - Outdated design harming user engagement
            - Modern UI with improved navigation

            **Slide 3**: Next Steps
            - Design prototype, user testing, Q&A"""

        else:
            # Default 4+ slides example
            return """**Slide 1**: Title
        - Website Redesign Proposal
        - Jane Doe, March 2025

        **Slide 2**: Challenge
        - Outdated design harming user engagement

        **Slide 3**: Solution
        - Modern UI with improved navigation

        **Slide 4**: Next Steps
        - Design prototype, user testing

        **Slide 5**: Q&A / Call to Action
        - Thank you for your time—questions?"""