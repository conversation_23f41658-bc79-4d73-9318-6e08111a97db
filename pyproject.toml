[tool.poetry]
name = "pptx-planner"
version = "0.1.0"
description = ""
authors = ["L33T-Snack3r <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
openai = "^1.91.0"
google-genai = "^1.21.1"
ipykernel = "^6.29.5"
dotenv = "^0.9.9"
pillow = "^11.2.1"
html2image = "^2.0.7"
selenium = "^4.33.0"
webdriver-manager = "^4.0.2"
jupyter = "^1.1.1"
loguru = "^0.7.3"
uvicorn = "^0.35.0"
fastapi = "^0.116.1"
python-dotenv = "^1.1.1"
reportlab = "^4.4.2"
anthropic = "^0.58.2"
tavily-python = "^0.7.10"


[tool.poetry.group.dev.dependencies]
ipykernel = "^6.29.5"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
