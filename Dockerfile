# Use a Python base image, ideally one that includes build essentials
FROM python:3.11-slim-bookworm

# Set environment variables for Poetry
ENV POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_IN_PROJECT=true \
    POETRY_NO_INTERACTION=1 \
    PATH="/opt/poetry/bin:$PATH"

# Install system dependencies
# These are often required for packages like selenium (chromium-browser) and reportlab (fonts, libfreetype6)
# Ensure to update apt lists and clean up after installation to keep the image size small
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    libsodium-dev \
    libffi-dev \
    libjpeg-dev \
    zlib1g-dev \
    libfreetype6-dev \
    libwebp-dev \
    xvfb \
    chromium \
    fonts-liberation \
    libu2f-udev \
    udev \
    # Clean up APT when done
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN curl -sSL https://install.python-poetry.org | python -

# Copy project files
WORKDIR /app
COPY pyproject.toml poetry.lock ./
COPY ./pptx_generation ./pptx_generation
COPY ./htmlrender ./htmlrender
COPY ./utils ./utils
COPY ./prompt ./prompt
COPY ./llm ./llm
COPY app.py ./

# Install project dependencies using Poetry
# Poetry will read pyproject.toml and poetry.lock to install exact versions
RUN poetry install --no-root --only main

# Ensure the temporary presentations directory exists and has correct permissions
# This is crucial if your app writes files here
RUN mkdir -p temp_presentations && chmod 777 temp_presentations

# Expose the port your FastAPI application listens on
# Cloud Run typically expects applications to listen on port 8080
ENV PORT 8080
EXPOSE $PORT

# Command to run your application using Uvicorn
# The --host 0.0.0.0 is important for Cloud Run to route traffic correctly
CMD ["poetry", "run", "uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8080"]
