
const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();

    // SLIDE 1: Hero A: Revolutionizing Presentation Creation
    const slide1 = pptx.addSlide();
    slide1.background = { color: '1A2A3A' };

    slide1.addText('Hero A: Revolutionizing Presentation Creation', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, color: 'FFFFFF', bold: true
    });

    slide1.addText('Strategic Overview of Our Innovative Solution for Comprehensive Presentation Development', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 18, color: 'B0B0B0'
    });

    slide1.addText('Presented by: TechConsult Solutions', {
        x: 0.5, y: 2.5, w: 9.0, h: 0.6,
        fontSize: 14, color: 'A0A0A0'
    });

    slide1.addText('Date: October 26, 2023', {
        x: 0.5, y: 3.2, w: 9.0, h: 0.6,
        fontSize: 14, color: 'A0A0A0'
    });

    // SLIDE 2: Introducing Hero A: The Future of Presentation Development
    const slide2 = pptx.addSlide();
    slide2.background = { color: '1A2A3A' };

    slide2.addText('Introducing Hero A: The Future of Presentation Development', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, color: 'FFFFFF', bold: true
    });

    slide2.addText('Hero A: AI-powered platform for automating & enhancing presentation creation.', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 18, color: 'E0E0E0'
    });

    slide2.addText('Leverages NLP, ML, and generative AI to transform data into compelling presentations.', {
        x: 0.5, y: 2.5, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    slide2.addText('Strategic Purpose: Reduce manual effort, time, and resources in presentation development.', {
        x: 0.5, y: 3.2, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    slide2.addText('Hero A ensures consistency, improves quality, and accelerates delivery for greater impact.', {
        x: 0.5, y: 3.9, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    // SLIDE 3: The Challenges of Traditional Presentation Creation
    const slide3 = pptx.addSlide();
    slide3.background = { color: '1A2A3A' };

    slide3.addText('The Challenges of Traditional Presentation Creation', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, color: 'FFFFFF', bold: true
    });

    slide3.addText('Key Challenges:', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 18, color: '00C0FF', bold: true
    });

    slide3.addText('* Time-Consuming & Resource-Intensive: Manual content drafting and design.', {
        x: 0.5, y: 2.5, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    slide3.addText('* Inconsistency & Brand Dilution: Lack of standardized templates and messaging.', {
        x: 0.5, y: 3.2, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    slide3.addText('* Scalability Limitations: Difficulty in rapidly generating tailored presentations.', {
        x: 0.5, y: 3.9, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    // SLIDE 4: Hero A: Core Capabilities for Intelligent Presentation Generation
    const slide4 = pptx.addSlide();
    slide4.background = { color: '1A2A3A' };

    slide4.addText('Hero A: Core Capabilities for Intelligent Presentation Generation', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 28, color: 'FFFFFF', bold: true
    });

    slide4.addText('Key Features:', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 18, color: '00C0FF', bold: true
    });

    slide4.addText('* AI-Driven Content Synthesis: Generates relevant, concise slide content.', {
        x: 0.5, y: 2.5, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    slide4.addText('* Intelligent Structure & Flow: Proposes optimal slide sequences and content flow.', {
        x: 0.5, y: 3.2, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    slide4.addText('* Dynamic Templating & Brand Compliance: Applies brand-compliant templates dynamically.', {
        x: 0.5, y: 3.9, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    // SLIDE 5: Transformative Impact: Expected Outcomes & Benefits of Adopting Hero A
    const slide5 = pptx.addSlide();
    slide5.background = { color: '1A2A3A' };

    slide5.addText('Transformative Impact: Expected Outcomes & Benefits of Adopting Hero A', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 28, color: 'FFFFFF', bold: true
    });

    slide5.addText('Quantifiable Improvements:', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 18, color: '00C0FF', bold: true
    });

    slide5.addText('* 80% Reduction in Creation Time: Complex presentations in hours, not days.', {
        x: 0.5, y: 2.5, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    slide5.addText('* 50% Cost Savings: Reduced reliance on external design and content resources.', {
        x: 0.5, y: 3.2, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    slide5.addText('* Enhanced Brand Consistency: Automated application of brand guidelines.', {
        x: 0.5, y: 3.9, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    // SLIDE 6: Hero A: Implementation Roadmap & Next Steps
    const slide6 = pptx.addSlide();
    slide6.background = { color: '1A2A3A' };

    slide6.addText('Hero A: Implementation Roadmap & Next Steps', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, color: 'FFFFFF', bold: true
    });

    slide6.addText('Proposed Rollout Plan:', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 18, color: '00C0FF', bold: true
    });

    slide6.addText('* Phase 1: Pilot Program & Foundation (Weeks 1-4) - Validate core functionality.', {
        x: 0.5, y: 2.5, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    slide6.addText('* Phase 2: Phased Departmental Rollout (Months 2-4) - Expand adoption & integrate.', {
        x: 0.5, y: 3.2, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    slide6.addText('* Phase 3: Full Adoption & Optimization (Month 5+) - Company-wide and enhancement.', {
        x: 0.5, y: 3.9, w: 9.0, h: 0.6,
        fontSize: 14, color: 'E0E0E0'
    });

    return pptx.writeFile({ fileName: 'presentation_20250713_134643.pptx' });
}

// Execute the presentation creation
createPresentation()
    .then(() => {
        console.log('PowerPoint file generated successfully!');
        console.log('File saved as: presentation_20250713_134643.pptx');
        process.exit(0);
    })
    .catch(error => {
        console.error('Error generating presentation:', error);
        process.exit(1);
    });
