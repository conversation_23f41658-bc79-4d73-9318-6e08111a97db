import requests
import json
import os
from datetime import datetime
import time

# --- Configuration ---
BASE_URL = "http://127.0.0.1:8000"  # Adjust if your FastAPI app runs on a different host/port

# List of queries to test
QUERIES = [
    "Make me a presentation about creating an Intelligent dashboard that monitoring CO2 emission using IoT. Make sure to elaborate on how we can demonstrate the information inside the dashboard in 6 slides",
    "Make me a presentation about migrating a legacy app written in COBOL to AWS. Suggest me a good approach to ensure the functionality and the mission critical nature of the app in 6 slides",
    "Make me a presentation about solving IT Duplication ticket using Gen AI. Knowing that my company using Jira and there are multiple overlapping ticket within the company in 6 slides",
    "Make a presentation about building a system which takes an internal company database of contract documents, and based on a user query, helps find the most relevant contract document in 6 slides",
    "Make me a presentation about a market research into last year Renewable Enegrgy. in 6 slides"
]

# --- Main Test Function ---
def run_api_pipeline(user_query: str, test_run_output_dir: str, query_index: int):
    """
    Runs the API test pipeline for a single query, saving results into a specified directory.
    
    Args:
        user_query (str): The user input query for presentation generation.
        test_run_output_dir (str): The base directory for this entire test run.
        query_index (int): The index of the current query (for unique filenames).
    
    Returns:
        bool: True if the pipeline completed successfully, False otherwise.
    """
    # Create a subdirectory for this specific query within the main test run folder
    # This ensures files from different queries don't overwrite each other
    query_specific_dir = os.path.join(test_run_output_dir, f"query_{query_index + 1}")
    os.makedirs(query_specific_dir, exist_ok=True)

    print(f"\n--- Starting test for query {query_index + 1}: '{user_query}' ---")
    print(f"Output for this query will be saved in: {query_specific_dir}")

    # Step 1: Generate Outline
    print("\n--- Step 1: Calling /generate-outline ---")
    outline_response_data = None
    try:
        outline_url = f"{BASE_URL}/generate-outline"
        outline_payload = {"user_input": user_query}
        print(f"Sending request to: {outline_url}")
        response = requests.post(outline_url, json=outline_payload)
        response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)

        # The /generate-outline endpoint directly returns the outline string
        outline_response_data = response.json()
        # Save the raw JSON response
        with open(os.path.join(query_specific_dir, "outline_response.json"), "w") as f:
            json.dump(outline_response_data, f, indent=4) 

        print(f"✅ /generate-outline successful. Outline (partial): {str(outline_response_data)[:100]}...")

    except requests.exceptions.RequestException as e:
        print(f"❌ Error during /generate-outline: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response content: {e.response.text}")
        return False # Indicate failure

    # Step 2: Generate Slides
    print("\n--- Step 2: Calling /generate-slides ---")
    slides_response_data = None
    if outline_response_data:
        try:
            slides_url = f"{BASE_URL}/generate-slides"
            slides_payload = {
                "outline": outline_response_data,
                "user_input": user_query
            }
            response = requests.post(slides_url, json=slides_payload)
            response.raise_for_status()

            slides_response_data = response.json()
            # Save the raw JSON response
            with open(os.path.join(query_specific_dir, "slides_response.json"), "w") as f:
                json.dump(slides_response_data, f, indent=4)

            # Basic validation
            assert "slides" in slides_response_data, "Expected 'slides' key in /generate-slides response"
            assert isinstance(slides_response_data["slides"], list), "'slides' should be a list"
            
            print(f"✅ /generate-slides successful. Generated {len(slides_response_data['slides'])} slides.")

        except requests.exceptions.RequestException as e:
            print(f"❌ Error during /generate-slides: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response content: {e.response.text}")
            return False
    else:
        print("Skipping /generate-slides as outline was not generated.")
        return False

    # Step 3: Render HTML
    print("\n--- Step 3: Calling /render-html ---")
    html_slides_response_data = None
    if slides_response_data and slides_response_data.get("slides"):
        try:
            render_html_url = f"{BASE_URL}/render-html"
            render_html_payload = {
                "slides": slides_response_data["slides"],
                "user_input": user_query
            }
            response = requests.post(render_html_url, json=render_html_payload)
            response.raise_for_status()

            html_slides_response_data = response.json()
            # Save the raw JSON response
            with open(os.path.join(query_specific_dir, "html_response.json"), "w") as f:
                json.dump(html_slides_response_data, f, indent=4)

            # Basic validation
            assert "html_slides" in html_slides_response_data, "Expected 'html_slides' key in /render-html response"
            assert isinstance(html_slides_response_data["html_slides"], list), "'html_slides' should be a list"

            print(f"✅ /render-html successful. Rendered {len(html_slides_response_data['html_slides'])} HTML slides.")

        except requests.exceptions.RequestException as e:
            print(f"❌ Error during /render-html: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response content: {e.response.text}")
            return False
    else:
        print("Skipping /render-html as slides were not generated.")
        return False

    # Step 4: Export PDF
    print("\n--- Step 4: Calling /export-pdf ---")
    pdf_download_url = None
    if html_slides_response_data and html_slides_response_data.get("html_slides"):
        try:
            export_pdf_url = f"{BASE_URL}/export-pdf"
            export_pdf_payload = {
                "html_slides": html_slides_response_data["html_slides"]
            }
            response = requests.post(export_pdf_url, json=export_pdf_payload)
            response.raise_for_status()

            pdf_response = response.json()
            # Save the raw JSON response
            with open(os.path.join(query_specific_dir, "pdf_response.json"), "w") as f:
                json.dump(pdf_response, f, indent=4)

            # Basic validation
            assert "download_url" in pdf_response, "Expected 'download_url' key in /export-pdf response"
            pdf_download_url = pdf_response["download_url"]

            print(f"✅ /export-pdf successful. Download URL: {pdf_download_url}")

            # Attempt to download the generated PDF file
            if pdf_download_url:
                download_full_url = f"{BASE_URL}{pdf_download_url}"
                try:
                    print(f"Attempting to download PDF from: {download_full_url}")
                    download_response = requests.get(download_full_url, stream=True)
                    download_response.raise_for_status()
                    
                    # Extract filename from URL or header, or create a fallback
                    filename = os.path.basename(pdf_download_url)
                    if not filename.endswith('.pdf'):
                        filename = f"generated_presentation_query_{query_index + 1}.pdf" # Fallback filename if not present

                    file_path = os.path.join(query_specific_dir, filename)
                    with open(file_path, 'wb') as f:
                        for chunk in download_response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    print(f"💾 PDF downloaded to: {file_path}")
                except requests.exceptions.RequestException as download_e:
                    print(f"❌ Error downloading PDF file: {download_e}")
                    return False # Indicate failure in download
        except requests.exceptions.RequestException as e:
            print(f"❌ Error during /export-pdf: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response content: {e.response.text}")
            return False
    else:
        print("Skipping /export-pdf as HTML slides were not rendered.")
        return False

    print(f"--- Test for query {query_index + 1} completed successfully! ---")
    return True # Indicate success

# --- Main execution loop ---
if __name__ == "__main__":
    overall_results = {}
    
    # Create a single timestamped directory for the entire test run
    run_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    main_output_base_dir = os.path.join("test/api_test_runs", run_timestamp) 
    os.makedirs(main_output_base_dir, exist_ok=True)
    print(f"All results for this test run will be saved in: '{main_output_base_dir}'")

    for i, query in enumerate(QUERIES):
        print(f"\n=====================================================================")
        print(f"           Running Test {i+1}/{len(QUERIES)}")
        print(f"=====================================================================")
        # Pass the single main output directory and the query index
        success = run_api_pipeline(query, main_output_base_dir, i)
        overall_results[query] = "SUCCESS" if success else "FAILED"
        # Add a small delay between runs to avoid overwhelming the server or hitting rate limits
        time.sleep(2)

    print("\n\n=====================================================================")
    print("                      API Automation Summary")
    print("=====================================================================")
    for query, status in overall_results.items():
        # Print only a truncated version of the query for readability in summary
        print(f"Query: '{query[:70]}...' -> Status: {status}")
    print("=====================================================================")
    print("All test results saved in:", main_output_base_dir)