
const PptxGenJS = require('pptxgenjs');

function createPresentation() {
    const pptx = new PptxGenJS();

    // SLIDE 1: Guinea Pig 101 - Proposal Slide
    const slide1 = pptx.addSlide();
    slide1.background = { color: 'F0F4F8' }; // Extracted from linear-gradient start color

    // TITLE
    slide1.addText('Guinea Pig 101', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 36, color: '2C3E50', bold: true
    });

    // CONTENT
    // Subtitle
    slide1.addText('Your Essential Guide to Cavy Companionship', {
        x: 0.5, y: 1.8, w: 9.0, h: 0.6,
        fontSize: 18, color: '555555', bold: false
    });

    // Presenter Info 1
    slide1.addText('Presented by: [Your Name/Consultancy Name]', {
        x: 0.5, y: 2.5, w: 9.0, h: 0.6,
        fontSize: 14, color: '777777', bold: false
    });

    // Presenter Info 2
    slide1.addText('Date: [Current Date]', {
        x: 0.5, y: 3.2, w: 9.0, h: 0.6,
        fontSize: 14, color: '777777', bold: false
    });

    // SLIDE 2: Introducing Guinea Pigs
    const slide2 = pptx.addSlide();
    slide2.background = { color: 'F0F4F8' }; // Extracted from linear-gradient start color

    // TITLE
    slide2.addText('Introducing Guinea Pigs', {
        x: 0.5, y: 0.8, w: 9.0, h: 0.8,
        fontSize: 32, color: '2C3E50', bold: true
    });

    // CONTENT (2-column layout, focusing on left text column)
    // Bullet 1 Main Point
    slide2.addText('Rodents, not pigs!', {
        x: 0.5, y: 1.8, w: 4.0, h: 0.6,
        fontSize: 18, color: '34495E', bold: true
    });

    // Bullet 1 Detail
    slide2.addText('* Despite name, they are rodents (Cavia porcellus), not swine. Corrects misconception.', {
        x: 0.5, y: 2.5, w: 4.0, h: 0.6,
        fontSize: 14, color: '555555', bold: false
    });

    // Bullet 2 Main Point
    slide2.addText('Popular pets worldwide', {
        x: 0.5, y: 3.2, w: 4.0, h: 0.6,
        fontSize: 18, color: '34495E', bold: true
    });

    // Bullet 2 Detail
    slide2.addText('* Gentle, social, easy care. Cherished companions globally.', {
        x: 0.5, y: 3.9, w: 4.0, h: 0.6,
        fontSize: 14, color: '555555', bold: false
    });

    return pptx.writeFile({ fileName: 'presentation_20250716_131428.pptx' });
}

// Execute the presentation creation
createPresentation()
    .then(() => {
        console.log('PowerPoint file generated successfully!');
        console.log('File saved as: presentation_20250716_131428.pptx');
        process.exit(0);
    })
    .catch(error => {
        console.error('Error generating presentation:', error);
        process.exit(1);
    });
